<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.harmonycloud</groupId>
        <artifactId>devops-development</artifactId>
        <version>2.4.0-SNAPSHOT</version>
    </parent>
    <groupId>cn.harmonycloud</groupId>
    <artifactId>program-common</artifactId>
    <version>2.4.0-SNAPSHOT</version>
    <name>program-common</name>
    <description>program-common</description>
    <properties></properties>
    <packaging>pom</packaging>
<modules>
    <module>harmony-common-core</module>
    <module>harmony-datasource-starter</module>
    <module>harmony-mybatis-starter</module>
    <module>harmony-openapi-starter</module>
    <module>harmony-redis-starter</module>
    <module>harmony-sequence-starter</module>
    <module>harmony-tenant-starter</module>
    <module>harmony-web-starter</module>
</modules>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.18</version>
            </plugin>
        </plugins>
    </build>

</project>
