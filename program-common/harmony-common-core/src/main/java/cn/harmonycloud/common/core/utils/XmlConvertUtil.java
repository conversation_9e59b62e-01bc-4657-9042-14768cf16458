package cn.harmonycloud.common.core.utils;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.common.core.helper.LoggerHelper;
import org.apache.commons.codec.Charsets;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.XMLConstants;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;

public class XmlConvertUtil {

    /**
     * xml to java bean
     *
     * @param clazz
     * @param xmlStr
     * @param <T>
     * @return
     */
    public static <T> T xmlToJava(Class<T> clazz, String xmlStr) {
        try {
            JAXBContext context = JAXBContext.newInstance(clazz);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            return (T) unmarshaller.unmarshal(new StringReader(xmlStr));
        } catch (JAXBException e) {
            throw new BusinessException("xml convert to javaBean error：" + e.getMessage());
        }
    }


    /**
     * java bean to xml
     *
     * @param obj
     * @param <T>
     * @return
     */
    public static <T> String javaToxml(T obj) {
        StringWriter sw = new StringWriter();
        try {

            JAXBContext context = JAXBContext.newInstance(obj.getClass());
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            marshaller.marshal(obj, sw);
        } catch (JAXBException e) {
            throw new BusinessException("javaBean convert to xml error：" + e.getMessage());
        }
        return sw.toString();
    }

    public static InputStream stringToInputStream(String string) {
        ByteArrayInputStream stream = new ByteArrayInputStream(string.getBytes());
        return stream;
    }

    public static String stream2str(InputStream inputStream) {
        BufferedReader in = new BufferedReader(new InputStreamReader(inputStream));
        StringBuilder buffer = new StringBuilder();
        String line = "";
        while (true) {
            try {
                if (null == (line = in.readLine())) {
                    break;
                }
            } catch (IOException e) {
                LoggerHelper.errorWithDebugger("IOException error", e);
            }
            buffer.append(line);
        }
        return buffer.toString();
    }

    public static Document stringToDoc(String xmlStr) {
        //字符串转XML
        Document doc = null;
        try {
            xmlStr = new String(xmlStr.getBytes(), Charsets.UTF_8);
            StringReader sr = new StringReader(xmlStr);
            InputSource is = new InputSource(sr);
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            String feature = "http://apache.org/xml/features/disallow-doctype-decl";
            factory.setFeature(feature, true);
            DocumentBuilder builder;
            builder = factory.newDocumentBuilder();
            doc = builder.parse(is);
        } catch (ParserConfigurationException e) {
            LoggerHelper.errorWithDebugger("ParserConfigurationException error ", e);
        } catch (SAXException e) {
            LoggerHelper.errorWithDebugger("SAXException error ", e);
        } catch (IOException e) {
            LoggerHelper.errorWithDebugger("IOException error ", e);
        }
        return doc;
    }

    public static String docToString(Document doc) {
        // XML转字符串
        String xmlStr = "";
        try {
            TransformerFactory tf = TransformerFactory.newInstance();
            tf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
            Transformer t = tf.newTransformer();
            t.setOutputProperty("encoding", "UTF-8");// 解决中文问题，试过用GBK不行
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            t.transform(new DOMSource(doc), new StreamResult(bos));
            xmlStr = bos.toString();
        } catch (TransformerConfigurationException e) {
            LoggerHelper.errorWithDebugger("TransformerConfigurationException error ", e);
        } catch (TransformerException e) {
            LoggerHelper.errorWithDebugger("TransformerException error ", e);
        }
        return xmlStr;
    }

    public static class MyByteArrayOutputStream extends ByteArrayOutputStream {
        public ByteArrayInputStream getByteArrayInputStream() {
            return new ByteArrayInputStream(this.toByteArray());
        }
    }
}
