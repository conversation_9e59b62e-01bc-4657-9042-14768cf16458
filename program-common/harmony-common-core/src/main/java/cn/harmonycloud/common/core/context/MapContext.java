package cn.harmonycloud.common.core.context;

import cn.hutool.core.thread.threadlocal.NamedThreadLocal;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @usage Map数据结构的Context实现，Context指能够线程内共享的变量
 * @date 2021/10/5 11:42 下午
 */
public class MapContext {
    private static final ThreadLocal<Map> context = new NamedThreadLocal<>("MapContext");

    public static Map getContext() {
        Map map = context.get();
        if (map == null) {
            map = new HashMap();
            context.set(map);
        }
        return map;
    }

    public static void setContext(Map map) {
        context.set(map);
    }

    public static void clear() {
        context.remove();
    }

    public static Object get(Object key) {
        return getContext().get(key);
    }

    public static Object put(Object key, Object value) {
        return getContext().put(key, value);
    }
}
