package cn.harmonycloud.web.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @describe
 * @author: wang<PERSON>an
 * @create: 2023/09/08
 **/

@Slf4j
public class HttpInterceptor implements HandlerInterceptor {
    String TOKEN_KEY = "Authorization";
    String HEADER_ORGAN_ID = "Amp-Organ-Id";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            //请求的url
            String requestURI = request.getRequestURI();
            String token = request.getHeader(TOKEN_KEY);
            String organId = request.getHeader(HEADER_ORGAN_ID);
            String requestMethod = request.getMethod();
            String queryString = request.getQueryString();
            String ipAddress = RequestUtil.getHttpServletRequestIpAddress(request);
            //String traceId = request.getHeader(AmpConstant.HEADER_TRACE_ID);
            log.info("HTTP REQUEST INFO ==> URL:{} Method:{} Query:{} IP:{}  OrganId:{} TOKEN:{}", requestURI, requestMethod, queryString, ipAddress, organId, token);
        } catch (Exception e) {
            log.error("请求拦截出错：", e);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }
}