package cn.harmonycloud.web.advice;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.common.core.exception.UnauthorizedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 全局异常处理
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public BaseResult handler(Exception e, HttpServletRequest request, HttpServletResponse response) {
        log.error("系统执行错误，请联系管理员", e);
        return BaseResult.failed("系统执行错误，请联系管理员！");
    }

    @ExceptionHandler(Throwable.class)
    @ResponseBody
    public ResponseEntity exceptionHandler(Throwable e) {
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        log.error("Exception:", e);
        return new ResponseEntity<>(BaseResult.failed("系统执行错误，请联系管理员!"), status);
    }

    @ExceptionHandler(value = {IllegalArgumentException.class, MethodArgumentTypeMismatchException.class})
    @ResponseBody
    public ResponseEntity exceptionHandler(IllegalArgumentException e) {
        log.error("IllegalArgumentException:", e);
        return new ResponseEntity<>(BaseResult.failed("请求输入参数有误，请检查输入内容"), HttpStatus.BAD_REQUEST);
    }

    /**
     * http exception advice
     *
     * @param e
     * @return
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public ResponseEntity handler(BusinessException e) {
        HttpStatus status = HttpStatus.OK;
        log.error("BusinessException:", e);
        return new ResponseEntity<>(BaseResult.failed(e.getMessage()), status);
    }

    @ExceptionHandler(UnauthorizedException.class)
    @ResponseBody
    public ResponseEntity unauthorized(UnauthorizedException e) {
        log.error("UnauthorizedException:", e);
        return new ResponseEntity<>(BaseResult.failed(e.getMessage()), HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    public ResponseEntity httpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error("HttpMessageNotReadableException:", e);
        return new ResponseEntity<>(BaseResult.failed("请求输入参数有误，请检查输入内容"), HttpStatus.BAD_REQUEST);
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseEntity methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        BindingResult result = e.getBindingResult();
        HttpStatus status = HttpStatus.resolve(400);
        StringBuilder rs = new StringBuilder();
        if (result.hasErrors()) {
            for (ObjectError p : result.getAllErrors()) {
                FieldError fieldError = (FieldError) p;
                rs.append(fieldError.getField() + fieldError.getDefaultMessage() + ".");
            }
        }

        return new ResponseEntity<>(BaseResult.failed(rs.toString()), status);
    }

    @ExceptionHandler({DataAccessException.class})
    @ResponseBody
    public ResponseEntity dataAccessException(DataAccessException e) {
        log.error("DataAccessException:", e);
        return new ResponseEntity(BaseResult.failed("输入执行有误，请联系管理员!"), HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
