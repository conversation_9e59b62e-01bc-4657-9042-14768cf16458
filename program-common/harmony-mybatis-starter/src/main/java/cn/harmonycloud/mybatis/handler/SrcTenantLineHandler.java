//package com.src.mybatis.handler;
//
//import cn.hutool.core.collection.CollUtil;
//import com.src.common.auth.repository.CurrentUser;
//import com.src.common.auth.repository.CurrentUserRepository;
//import com.src.common.exception.BusinessException;
//import com.src.common.tenant.dto.CurrentQueryTenant;
//import com.src.common.tenant.repository.CurrentQueryTenantRepository;
//import net.sf.jsqlparser.expression.Expression;
//import net.sf.jsqlparser.expression.LongValue;
//import net.sf.jsqlparser.expression.ValueListExpression;
//import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
//
//import javax.annotation.Nullable;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Objects;
//import java.util.Set;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @date 2020/12/17 11:23 上午
// */
//public class SrcTenantLineHandler implements ITenantLineHandler {
//    private Set<String> ignoreTable = initIgnoreTable();
//
//    @Override
//    public boolean activeMultiSelect() {
//        CurrentQueryTenant currentQueryTenant = CurrentQueryTenantRepository.get();
//        if (isActive(currentQueryTenant)) {
//            return true;
//        }
//        return false;
//    }
//
//    private static boolean isActive(CurrentQueryTenant currentQueryTenant) {
//        return currentQueryTenant != null && CollUtil.isNotEmpty(currentQueryTenant.getTenantIds());
//    }
//
//    @Override
//    public Expression getTenantId() {
//        return new LongValue(getCurrentTenantId());
//    }
//
//    /**
//     * 获取当前tenantId
//     * 1、 优先获取 Request#Header中的
//     * 2、 再获取当前用户底下的租户信息
//     *
//     * @return 租户ID
//     */
//    public static Long getCurrentTenantId() {
//        CurrentQueryTenant currentQueryTenant = CurrentQueryTenantRepository.get();
//        if (currentQueryTenant != null && currentQueryTenant.getTenantId() != null) {
//            return currentQueryTenant.getTenantId();
//        }
//        CurrentUser currentUser = CurrentUserRepository.get();
//        if (currentUser != null && currentUser.getTenantId() != null) {
//            return currentUser.getTenantId();
//        }
//        throw new BusinessException("缺失对应租户信息");
//    }
//
//    public static @Nullable Long getCurrentTenantIdNullable() {
//        try {
//            return getCurrentTenantId();
//        } catch (BusinessException e) {
//            return null;
//        }
//    }
//
//    @Override
//    public Expression getTenantIds() {
//        ValueListExpression valueListExpression = new ValueListExpression();
//        List<Expression> list = getCurrentPolyTenantIds()
//                .stream().map(LongValue::new).collect(Collectors.toList());
//        valueListExpression.setExpressionList(new ExpressionList(list));
//        return valueListExpression;
//    }
//
//    /**
//     * 获取当前聚合租户IDs
//     *
//     * @return
//     */
//    public static Set<Long> getCurrentPolyTenantIds() {
//        if (isActive(CurrentQueryTenantRepository.get())) {
//            return CurrentQueryTenantRepository.get().getTenantIds();
//        }
//        throw new BusinessException("缺失对应租户信息");
//    }
//
//    public static @Nullable
//    Set<Long> getCurrentPolyTenantIdsNullable() {
//        try {
//            return getCurrentPolyTenantIds();
//        } catch (BusinessException e) {
//            return null;
//        }
//    }
//
//    public static boolean isGetAll() {
//        if (CurrentUserRepository.get() == null) {
//            return false;
//        }
//        // 平台侧管理远也能看到全部
//        return CurrentUserRepository.get().isAdmin() || CurrentUserRepository.get().isPlatForm();
//    }
//
//    @Override
//    public boolean temporalIgnore() {
//        return CurrentQueryTenantRepository.get() != null && CurrentQueryTenantRepository.get().getTemporalIgnore();
//    }
//
//    @Override
//    public boolean ignoreTable(String tableName) {
//        // 获取CurrentUser中的isAdmin属性,若为true则返回true
//        return ignoreTable.contains(tableName) || temporalIgnore();
//    }
//
//    private Set<String> initIgnoreTable() {
//        Set<String> ignoreTables = new HashSet<>();
//        // 用户服务
//        ignoreTables.add("user");
//        ignoreTables.add("tenant");
//        ignoreTables.add("user_tenant_relation");
//        ignoreTables.add("org");
//        ignoreTables.add("role_menu_relation");
//        ignoreTables.add("user_role_relation");
//        ignoreTables.add("menu");
//        // 开发者服务
//        ignoreTables.add("service_info");
//        ignoreTables.add("route");
//        ignoreTables.add("sys_config");
//        ignoreTables.add("frontend_info");
//        ignoreTables.add("dict");
//        ignoreTables.add("dict_item");
//        ignoreTables.add("service_additional_info");
//        //能力服务
//        ignoreTables.add("ability");
//        ignoreTables.add("ability_subscription_record");
//        ignoreTables.add("resource");
//        // 消息服务
//        ignoreTables.add("record");
//        ignoreTables.add("record_item");
//        ignoreTables.add("template");
//        ignoreTables.add("mail_config");
//        ignoreTables.add("record_role_relation");
//        //资产服务
//        ignoreTables.add("asset_mapping");
//        ignoreTables.add("asset_mapping_port");
//        ignoreTables.add("change_history");
//        ignoreTables.add("loophole");
//        ignoreTables.add("risk");
//        ignoreTables.add("web_map");
//        ignoreTables.add("request_response");
//        ignoreTables.add("asset_task");
//        ignoreTables.add("asset_port");
//        ignoreTables.add("loophole_asset_relation");
//        return ignoreTables;
//    }
//}
