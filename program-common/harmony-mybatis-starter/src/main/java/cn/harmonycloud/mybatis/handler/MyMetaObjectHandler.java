package cn.harmonycloud.mybatis.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

/**
 * @Usage: meta info自动填充处理器
 * @Author: by xiaoyx
 * @Date: 2020/9/16
 */
public class MyMetaObjectHandler implements MetaObjectHandler {
//
//    @Override
//    public void insertFill(MetaObject metaObject) {
//        LocalDateTime now = LocalDateTime.now();
//        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
//        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
////        CurrentUser currentUser = CurrentUserRepository.get();
////        if(Objects.nonNull(currentUser)){
////            this.strictInsertFill(metaObject, "createBy", Long.class, currentUser.getUserId());
////        }else {
////            this.strictInsertFill(metaObject, "createBy", Long.class, -1L);
////        }
//    }
//
//    @Override
//    public void updateFill(MetaObject metaObject) {
//        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
//
////        CurrentUser currentUser = CurrentUserRepository.get();
////        if(Objects.nonNull(currentUser)){
////            this.strictUpdateFill(metaObject, "updateBy", Long.class, currentUser.getUserId());
////        }else {
////            this.strictUpdateFill(metaObject, "updateBy", Long.class, -1L);
////        }
//
//    }

    @Override
    public void insertFill(MetaObject metaObject) {
        this.setFieldValByName("createTime", LocalDateTime.now(),metaObject);
        this.setFieldValByName("updateTime", LocalDateTime.now(),metaObject);
    }
    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateTime",LocalDateTime.now(),metaObject);
    }
}