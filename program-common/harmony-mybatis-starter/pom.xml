<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.harmonycloud</groupId>
        <artifactId>program-common</artifactId>
        <version>2.4.0-SNAPSHOT</version>
    </parent>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modelVersion>4.0.0</modelVersion>
    <version>2.4.0-SNAPSHOT</version>
    <artifactId>harmony-mybatis-starter</artifactId>
    <packaging>jar</packaging>

    <properties>
        <jsqlparser.version>4.3</jsqlparser.version>
        <mybatis-plus.version>3.4.0</mybatis-plus.version>
        <mysql.connector.version>8.0.29</mysql.connector.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cn.harmonycloud</groupId>
            <artifactId>harmony-common-core</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--分布式ID-->
        <dependency>
            <groupId>cn.harmonycloud</groupId>
            <artifactId>harmony-sequence-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.connector.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>${jsqlparser.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>