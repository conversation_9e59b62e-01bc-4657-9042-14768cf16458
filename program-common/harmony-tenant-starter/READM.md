# harmony-tenant-starter
功能介绍：
1. 自动从请求header中获取Token（Authorization）、租户ID（Amp-Organ-Id）、用户基本信息（userInfo）、资源信息（Resource），并放到ThreadLocal中。注意以上信息默认不做强制必传校验，如果需要做校验则添加如下配置即可，校验不通过会抛出BusinessException异常。
```yaml
# 开启多租户配置
hc-tenant:
  biz:
    # 需要校验的header key
    check-header-key:
      - Authorization
      - Amp-Organ-Id
      - userInfo
      - Resource
```
2. 可以使用【TenantContextHolder】工具类来获取Token、租户ID、用户基本信息、资源信息。
3. 开启多租户，添加hc-tenant. mybatis-plus.enable=ture 即可开启多租户
```yaml
# 开启多租户配置
hc-tenant:
  mybatis-plus:
    # 开启mybatisPlus多租户
    enable: true
  biz:
    # 租户字段 默认tenant_id
    tenant-column: tenant_id
    # 全部是租户表
    all-tenant-tables: true
    # 如果allTenantTables为false，则需要在这里配置租户表
    tenant-tables: 
      - hms_messaage
```
