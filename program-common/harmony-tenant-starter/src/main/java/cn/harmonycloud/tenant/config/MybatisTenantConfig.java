package cn.harmonycloud.tenant.config;

import cn.harmonycloud.tenant.mybatis.MybatisTenantHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;


//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@EnableConfigurationProperties(TenantConfigProperties.class)
public class MybatisTenantConfig {

    private final TenantConfigProperties properties;

    public MybatisTenantConfig(TenantConfigProperties properties) {
        this.properties = properties;
    }

    /**
     * 创建租户维护处理器对象
     *
     * @return 处理后的租户维护处理器
     */
    @Bean
    @ConditionalOnProperty(name = "hc-tenant.mybatis-plus.enable", havingValue = "true")
    public MybatisPlusInterceptor microTenantHandler() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        MybatisTenantHandler mybatisTenantHandler = new MybatisTenantHandler(properties);
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(mybatisTenantHandler));
        return interceptor;
    }

}
