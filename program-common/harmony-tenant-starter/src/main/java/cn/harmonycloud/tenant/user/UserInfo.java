package cn.harmonycloud.tenant.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @describe 基本用户信息
 * @author: wang<PERSON><PERSON>
 * @create: 2022/03/07
 **/
@Data
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 6894007544040797449L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "账户名")
    private String username;

    @ApiModelProperty(value = "性别 0女 1男")
    private Integer gender;

    @ApiModelProperty(value = "类型 1平台用户 2组织用户")
    private Integer type;

    @ApiModelProperty(value = "状态 0.正常 1.锁定")
    private Integer status;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String description;
}
