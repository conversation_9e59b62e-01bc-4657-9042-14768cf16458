//package cn.harmonycloud.tenant.interceptor;
//
//import cn.harmonycloud.common.core.constant.SecurityConstants;
//import cn.harmonycloud.tenant.TenantContextHolder;
//import com.alibaba.fastjson.JSON;
//import feign.RequestInterceptor;
//import feign.RequestTemplate;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpHeaders;
//
///**
// * Feign租户拦截器
// */
//@Slf4j
//public class FeignTenantInterceptor implements RequestInterceptor {
//    @Override
//    public void apply(RequestTemplate requestTemplate) {
//        requestTemplate.header(HttpHeaders.AUTHORIZATION,TenantContextHolder.getToken());
//        requestTemplate.header(SecurityConstants.TENANT_ID, TenantContextHolder.getTenantId());
//        requestTemplate.header(SecurityConstants.MICRO_USER_INFO, JSON.toJSONString(TenantContextHolder.getUserInfo()));
//        requestTemplate.header(SecurityConstants.RESOURCE,TenantContextHolder.getResource());
//    }
//}
