package cn.harmonycloud.openapi;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.SecurityScheme;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.ArrayList;

/**
 * @Usage: swagger API 配置
 * @Author: gjq
 * @Date: 2019/3/28 10:28 AM
 * todo SecurityScheme可配置
 */
public class SwaggerConfig {
    @Value("${project.openapi.name:default}")
    private String applicationName;
    @Value("${project.openapi.version:v1.0}")
    private String version;
    @Value("${project.openapi.description:}")
    private String description;
    @Value("${project.openapi.group:public}")
    private String group;
    @Value("${project.openapi.packages:cn.harmonycloud}")
    private String packages;
    @Value("${project.openapi.link:http:harmonycloud.cn}")
    private String link;

    @Bean
    public Docket docker() {
       //  OAS_30 文档规范
        return new Docket(DocumentationType.OAS_30)
              //  apiInfo： 添加api详情信息，参数为ApiInfo类型的参数，这个参数包含了第二部分的所有信息比如标题、描述、版本之类的，开发中一般都会自定义这些信息
                .apiInfo(apiInfo())
                .groupName(group)
              //  配置是否启用Swagger，如果是false，在浏览器将无法访问，默认是true
                .enable(true)
                .select()
            //    apis： 添加过滤条件,
                .apis(RequestHandlerSelectors.basePackage(packages))
                .build();
    }

    private ApiInfo apiInfo() {
        Contact contact = new Contact("名字：高家祺", "个人链接：http:************:31200/gaojiaqi", "邮箱：<EMAIL>");

        return new ApiInfo(
                applicationName,  //标题
                description, // 描述
                "版本内容：" + version,  //版本
                "链接：" + link,  //组织链接
                contact,  //联系人信息
                "许可：Apach 2.0 ",  //许可
                "许可链接：XXX",  //许可连接
                new ArrayList<>() //扩展
        );
    }
    @Bean
    @ConditionalOnProperty(prefix = "project.openapi.homepage", name = "enable", havingValue = "true")
    public HomePageConfig homePageConfig() {
        return new HomePageConfig();
    }


    private SecurityScheme apiKeySecuritySchema() {
        return new SecurityScheme()
                .name("Authorization")
                .description("JWT TOKEN")
                .in(SecurityScheme.In.HEADER)
                .type(SecurityScheme.Type.APIKEY);
    }

}
