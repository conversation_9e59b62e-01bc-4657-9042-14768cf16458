package cn.harmony.sequence;

import cn.harmony.sequence.builder.SnowflakeSeqBuilder;
import cn.harmony.sequence.properties.SequenceDbProperties;
import cn.harmony.sequence.properties.SequenceSnowflakeProperties;
import cn.harmony.sequence.sequence.Sequence;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自动配置类
 */
@Configuration
@ConditionalOnMissingBean(Sequence.class)
public class SequenceAutoConfiguration {

    /**
     * snowflak 算法作为发号器实现
     *
     * @param properties
     * @return
     */
    @Bean
    @ConditionalOnProperty(name = "project.sequence.snowflake.enable", matchIfMissing = true)
    public Sequence snowflakeSequence(SequenceSnowflakeProperties properties) {
        return SnowflakeSeqBuilder
                .create()
                .datacenterId(properties.getDataCenterId())
                .build();
    }

    @Bean
    @ConditionalOnProperty(name = "project.sequence.db")
    public SequenceDbProperties sequenceDbProperties() {
        return new SequenceDbProperties();
    }


    @Bean
    public SequenceSnowflakeProperties sequenceSnowflakeProperties() {
        return new SequenceSnowflakeProperties();
    }
}
