package cn.harmony.sequence.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 发号器DB配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "project.sequence.db")
public class SequenceDbProperties extends BaseSequenceProperties {
    /**
     * 表名称
     */
    private String tableName = "sys_sequence";
    /**
     * 重试次数
     */
    private int retryTimes = 1;

}
