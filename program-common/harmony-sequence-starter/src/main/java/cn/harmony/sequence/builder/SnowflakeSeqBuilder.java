package cn.harmony.sequence.builder;

import cn.harmony.sequence.sequence.Sequence;
import cn.harmony.sequence.sequence.impl.SnowflakeSequence;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 基于雪花算法，序列号生成器构建者
 */
@Slf4j
public class SnowflakeSeqBuilder implements SeqBuilder {

    /**
     * 数据中心ID，值的范围在[0,31]之间，一般可以设置机房的IDC[必选]
     */
    private long datacenterId;
    /**
     * 工作机器ID，值的范围在[0,31]之间，一般可以设置机器编号[必选]
     */
    private long workerId;

    public static SnowflakeSeqBuilder create() {
        return new SnowflakeSeqBuilder();
    }

    @Override
    public Sequence build() {
        SnowflakeSequence sequence = new SnowflakeSequence();
        sequence.setDatacenterId(this.datacenterId);
        sequence.setWorkerId(getWorkerID());
        return sequence;
    }

    public SnowflakeSeqBuilder datacenterId(long datacenterId) {
        this.datacenterId = datacenterId;
        return this;
    }

    /**
     * 根据IP地址生成实例间不重复的id
     *
     * @return
     */
    public long getWorkerID() {
        InetAddress address;
        try {
            address = InetAddress.getLocalHost();
        } catch (final UnknownHostException e) {
            throw new IllegalStateException(
                    "Cannot get LocalHost InetAddress, please check your network!");
        }
        byte[] ipAddressByteArray = address.getAddress();
        long workId = (
                ((ipAddressByteArray[ipAddressByteArray.length - 2] & 0B11) << Byte.SIZE) +
                        (ipAddressByteArray[ipAddressByteArray.length - 1] & 0xFF));
        log.info("Sequence workId init success: {}", workId);
        return workId;
    }
}
