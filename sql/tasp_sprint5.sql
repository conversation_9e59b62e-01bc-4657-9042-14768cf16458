SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for scaffold_component
-- ----------------------------
DROP TABLE IF EXISTS `scaffold_component`;
CREATE TABLE `scaffold_component` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '组件唯一标识（主键）',
                                      `name` varchar(128) NOT NULL COMMENT '组件名称',
                                      `name_en` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组件英文名',
                                      `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组件功能描述',
                                      `category` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组件分类（如 security、data）',
                                      `package_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '组件类型（mave/npm）',
                                      `dep_default` tinyint(1) DEFAULT NULL COMMENT '默认组件（工程必须）',
                                      `dep_content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '组件依赖内容（maven、npm坐标内容）',
                                      `config_content` json DEFAULT NULL COMMENT '配置文件内容（json数组）',
                                      `documentation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文档地址',
                                      `icon` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组件图标',
                                      `bg_color` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '背景色',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                      `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                      `del_flag` tinyint(1) unsigned zerofill DEFAULT '0' COMMENT '删除标识',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='脚手架组件表';

-- ----------------------------
-- Records of scaffold_component
-- ----------------------------
BEGIN;
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (1, 'Web开发基础能力', 'trina-web-starter', '提供基础的Web开发功能', '认证与安全', 'maven', 1, '<dependency>\n		<groupId>cn.harmonycloud</groupId>\n		<artifactId>harmony-redis-starter</artifactId>\n		<version>${project.version}</version>\n</dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'Cloud', '131,98,238', '2025-07-09 11:17:13', '2025-07-09 11:17:16', 1, 1, 0);
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (2, '跨站防护基础能力', 'trina-xss-starter', '用于增强网站的安全性，防止跨站脚本攻击', '认证与安全', 'maven', 1, '<dependency>\r\n    <groupId>com.trinasolar</groupId>\r\n    <artifactId>trina-xss-starter</artifactId>\r\n</dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'Shield', '221,82,76', '2025-07-09 14:51:35', '2025-07-09 14:51:38', 1, 1, 0);
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (3, '安全认证与权限控制', 'trina-security-starter', '提供用户认证和权限管理的功能', '认证与安全', 'maven', 1, '<dependency>\r\n    <groupId>com.trinasolar</groupId>\r\n    <artifactId>trina-security-starter</artifactId>\r\n</dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'Users', '76,131,238', '2025-07-09 15:35:15', '2025-07-09 15:35:53', 1, 1, 0);
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (4, '自动生成OpenAPI文档', 'trina-openapi-starter', '便于API的集成和测试，自动生成文档', 'API文档', 'maven', 1, '<dependency>\r\n    <groupId>com.trinasolar</groupId>\r\n    <artifactId>trina-openapi-starter</artifactId>\r\n</dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'BookOpen', '76,160,84', '2025-07-09 15:35:18', '2025-07-09 15:35:55', 1, 1, 0);
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (5, 'ORM框架增强', 'trina-mybatis-starter', '支持MyBatis框架，简化数据库操作', '常用工具', 'maven', 0, '<dependency>\r\n            <groupId>com.trinasolar</groupId>\r\n            <artifactId>trina-mybatis-starter</artifactId>\r\n        </dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'Server', '51,101,227', '2025-07-09 15:35:38', '2025-07-09 15:35:59', 1, 1, 0);
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (6, '分布式缓存', 'trina-redis-starter', '提供Redis缓存支持，提升系统性能', '常用工具', 'maven', 0, '<dependency>\r\n    <groupId>com.trinasolar</groupId>\r\n    <artifactId>trina-redis-starter</artifactId>\r\n</dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'Cpu', '203,58,50', '2025-07-09 15:35:41', '2025-07-09 15:36:02', 1, 1, 0);
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (7, '声明式HTTP客户端', 'trina-openfeign-starter', '用于声明式Web服务客户端，简化HTTP调用', '微服务治理', 'maven', 0, '<dependency>\r\n    <groupId>com.trinasolar</groupId>\r\n    <artifactId>trina-openfeign-starter</artifactId>\r\n</dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'Cloud', '131,98,238', '2025-07-09 15:35:44', '2025-07-09 15:36:05', 1, 1, 0);
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (8, '服务注册发现与配置中心', 'trina-nacos-starter', '提供动态服务发现、配置和服务管理平台', '微服务治理', 'maven', 0, '<dependency>\r\n    <groupId>com.trinasolar</groupId>\r\n    <artifactId>trina-nacos-starter</artifactId>\r\n</dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'Network', '233,122,53', '2025-07-09 15:35:47', '2025-07-09 15:36:08', 1, 1, 0);
INSERT INTO `scaffold_component` (`id`, `name`, `name_en`, `description`, `category`, `package_type`, `dep_default`, `dep_content`, `config_content`, `documentation`, `icon`, `bg_color`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`) VALUES (9, '流量治理', 'trina-sentinel-starter', '用于流量控制、熔断降级，保障系统稳定性', '微服务治理', 'maven', 0, '<dependency>\r\n    <groupId>com.trinasolar</groupId>\r\n    <artifactId>trina-nacos-sentinel</artifactId>\r\n</dependency>', '{\"path\": \"src/main/resources/application.yml\", \"content\": \"security.auth.enabled: true\\nsecurity.auth.token-expire: 3600\"}', NULL, 'Activity', '226,180,63', '2025-07-09 15:35:50', '2025-07-09 15:36:11', 1, 1, 0);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;