<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.harmonycloud</groupId>
        <artifactId>devops-development</artifactId>
        <version>2.4.0-SNAPSHOT</version>
    </parent>

    <groupId>cn.harmonycloud</groupId>
    <artifactId>devops-development-api</artifactId>
    <version>2.4.0-SNAPSHOT</version>
    <name>devops-development-api</name>
    <description>Harmony-cloud默认代码生成</description>
    <packaging>jar</packaging>

    <properties>

    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.harmonycloud</groupId>
            <artifactId>harmony-common-core</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>cn.harmonycloud</groupId>-->
        <!--            <artifactId>harmony-remote-starter</artifactId>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>cn.harmonycloud</groupId>-->
        <!--            <artifactId>pmp-api</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.7.0</version>
        </dependency>
        <!--gitlab官方包-->
        <dependency>
            <groupId>org.gitlab4j</groupId>
            <artifactId>gitlab4j-api</artifactId>
            <version>4.19.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.maven.scm</groupId>
            <artifactId>maven-scm-provider-gitexe</artifactId>
            <version>2.0.0-M3</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jgit</groupId>
            <artifactId>org.eclipse.jgit</artifactId>
            <version>5.7.0.202003110725-r</version>
        </dependency>
        <dependency>
            <groupId>org.tmatesoft.svnkit</groupId>
            <artifactId>svnkit</artifactId>
            <version>1.8.13</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.mapstruct</groupId>-->
        <!--            <artifactId>mapstruct-processor</artifactId>-->
        <!--            <version>1.4.2.Final</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.mapstruct</groupId>-->
        <!--            <artifactId>mapstruct</artifactId>-->
        <!--            <version>1.4.2.Final</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.5.Final</version>
            <scope>compile</scope>
        </dependency>
        <!--feign 依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <!-- 确保lombok出现在mapstruct之前 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <parameters>true</parameters>
                    <source>8</source>
                    <target>8</target>
                    <!--                    <release>11</release>-->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
