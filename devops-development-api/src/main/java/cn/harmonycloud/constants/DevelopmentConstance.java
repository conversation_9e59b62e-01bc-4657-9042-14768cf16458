package cn.harmonycloud.constants;

/**
 * 研发工作台相关枚举值
 */
public interface DevelopmentConstance {

    // 研发阶段类型
    interface StageType {
        int DEV = 1;
        int TEST = 2;
        int MAIN = 3;
        int PROD = 4;
    }

    // 检修单审批状态
    interface  OrderApplyStatus{
        int DRAFT = 0; // 草稿
        int FORMAL = 1; // 正式
    }

    // 检修单checklist状态
    interface  ChecklistStatus{
        int FAIL = 0; // 失败
        int SUSS = 1; // 成功
        int NOT_DETECTED = 2; // 未检测
    }

    // 环境部署类型
    interface  EnvDeployType{
        Integer virtually = 1;
        Integer host = 2;
        Integer k8s = 4;
    }
}
