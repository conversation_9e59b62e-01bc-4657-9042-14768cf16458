package cn.harmonycloud.issue.model.dto.v2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

/* loaded from: ProjectV2.class */
public class ProjectV2 {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(name = "项目编码")
    private String projectCode;
    @ApiModelProperty(name = "项目名称")
    private String name;
    @ApiModelProperty(name = "空间名称")
    private String spaceName;
    @ApiModelProperty(name = "描述")
    private String description;
    @ApiModelProperty(name = "归属组织id")
    private String belongOrgId;
    @ApiModelProperty(name = "流程字典id")
    private Long processDictionaryId;
    @ApiModelProperty(name = "类型字典id")
    private Long typeDictionaryId;
    @ApiModelProperty(name = "项目来源id")
    private String sourceId;
    @ApiModelProperty(name = "项目来源字典id")
    private Long sourceDictionaryId;
    @ApiModelProperty(name = "项目状态字典id")
    private Long statusDictionaryId;
    @TableField(exist = false)
    private String queryParam;
    @TableField(exist = false)
    private Long userIdList;
    @ApiModelProperty(name = "模版ID")
    private Long templateId;
    @ApiModelProperty(name = "edep项目ID")
    private Long edepId;
    @ApiModelProperty(name = "应用管理项目ID")
    private String appProjectId;
    @ApiModelProperty(name = "开始时间")
    private String startTime;
    @DateTimeFormat(pattern = "yyyy-MM-ddTHH:mm:ss")
    @ApiModelProperty(name = "完成时间")
    private String endTime;
    @ApiModelProperty(name = "租户id")
    private Long tenantId;
    @ApiModelProperty(name = "系统id")
    private Long systemId;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("修改时间")
    private String updateTime;
    @ApiModelProperty("创建用户")
    private Long createBy;
    @ApiModelProperty("修改用户")
    private Long updateBy;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectCode(final String projectCode) {
        this.projectCode = projectCode;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setSpaceName(final String spaceName) {
        this.spaceName = spaceName;
    }

    public void setDescription(final String description) {
        this.description = description;
    }

    public void setBelongOrgId(final String belongOrgId) {
        this.belongOrgId = belongOrgId;
    }

    public void setProcessDictionaryId(final Long processDictionaryId) {
        this.processDictionaryId = processDictionaryId;
    }

    public void setTypeDictionaryId(final Long typeDictionaryId) {
        this.typeDictionaryId = typeDictionaryId;
    }

    public void setSourceId(final String sourceId) {
        this.sourceId = sourceId;
    }

    public void setSourceDictionaryId(final Long sourceDictionaryId) {
        this.sourceDictionaryId = sourceDictionaryId;
    }

    public void setStatusDictionaryId(final Long statusDictionaryId) {
        this.statusDictionaryId = statusDictionaryId;
    }

    public void setQueryParam(final String queryParam) {
        this.queryParam = queryParam;
    }

    public void setUserIdList(final Long userIdList) {
        this.userIdList = userIdList;
    }

    public void setTemplateId(final Long templateId) {
        this.templateId = templateId;
    }

    public void setEdepId(final Long edepId) {
        this.edepId = edepId;
    }

    public void setAppProjectId(final String appProjectId) {
        this.appProjectId = appProjectId;
    }

    public void setStartTime(final String startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(final String endTime) {
        this.endTime = endTime;
    }

    public void setTenantId(final Long tenantId) {
        this.tenantId = tenantId;
    }

    public void setSystemId(final Long systemId) {
        this.systemId = systemId;
    }

    public void setCreateTime(final String createTime) {
        this.createTime = createTime;
    }

    public void setUpdateTime(final String updateTime) {
        this.updateTime = updateTime;
    }

    public void setCreateBy(final Long createBy) {
        this.createBy = createBy;
    }

    public void setUpdateBy(final Long updateBy) {
        this.updateBy = updateBy;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ProjectV2) {
            ProjectV2 other = (ProjectV2) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$processDictionaryId = getProcessDictionaryId();
                Object other$processDictionaryId = other.getProcessDictionaryId();
                if (this$processDictionaryId == null) {
                    if (other$processDictionaryId != null) {
                        return false;
                    }
                } else if (!this$processDictionaryId.equals(other$processDictionaryId)) {
                    return false;
                }
                Object this$typeDictionaryId = getTypeDictionaryId();
                Object other$typeDictionaryId = other.getTypeDictionaryId();
                if (this$typeDictionaryId == null) {
                    if (other$typeDictionaryId != null) {
                        return false;
                    }
                } else if (!this$typeDictionaryId.equals(other$typeDictionaryId)) {
                    return false;
                }
                Object this$sourceDictionaryId = getSourceDictionaryId();
                Object other$sourceDictionaryId = other.getSourceDictionaryId();
                if (this$sourceDictionaryId == null) {
                    if (other$sourceDictionaryId != null) {
                        return false;
                    }
                } else if (!this$sourceDictionaryId.equals(other$sourceDictionaryId)) {
                    return false;
                }
                Object this$statusDictionaryId = getStatusDictionaryId();
                Object other$statusDictionaryId = other.getStatusDictionaryId();
                if (this$statusDictionaryId == null) {
                    if (other$statusDictionaryId != null) {
                        return false;
                    }
                } else if (!this$statusDictionaryId.equals(other$statusDictionaryId)) {
                    return false;
                }
                Object this$userIdList = getUserIdList();
                Object other$userIdList = other.getUserIdList();
                if (this$userIdList == null) {
                    if (other$userIdList != null) {
                        return false;
                    }
                } else if (!this$userIdList.equals(other$userIdList)) {
                    return false;
                }
                Object this$templateId = getTemplateId();
                Object other$templateId = other.getTemplateId();
                if (this$templateId == null) {
                    if (other$templateId != null) {
                        return false;
                    }
                } else if (!this$templateId.equals(other$templateId)) {
                    return false;
                }
                Object this$edepId = getEdepId();
                Object other$edepId = other.getEdepId();
                if (this$edepId == null) {
                    if (other$edepId != null) {
                        return false;
                    }
                } else if (!this$edepId.equals(other$edepId)) {
                    return false;
                }
                Object this$tenantId = getTenantId();
                Object other$tenantId = other.getTenantId();
                if (this$tenantId == null) {
                    if (other$tenantId != null) {
                        return false;
                    }
                } else if (!this$tenantId.equals(other$tenantId)) {
                    return false;
                }
                Object this$systemId = getSystemId();
                Object other$systemId = other.getSystemId();
                if (this$systemId == null) {
                    if (other$systemId != null) {
                        return false;
                    }
                } else if (!this$systemId.equals(other$systemId)) {
                    return false;
                }
                Object this$createBy = getCreateBy();
                Object other$createBy = other.getCreateBy();
                if (this$createBy == null) {
                    if (other$createBy != null) {
                        return false;
                    }
                } else if (!this$createBy.equals(other$createBy)) {
                    return false;
                }
                Object this$updateBy = getUpdateBy();
                Object other$updateBy = other.getUpdateBy();
                if (this$updateBy == null) {
                    if (other$updateBy != null) {
                        return false;
                    }
                } else if (!this$updateBy.equals(other$updateBy)) {
                    return false;
                }
                Object this$projectCode = getProjectCode();
                Object other$projectCode = other.getProjectCode();
                if (this$projectCode == null) {
                    if (other$projectCode != null) {
                        return false;
                    }
                } else if (!this$projectCode.equals(other$projectCode)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$spaceName = getSpaceName();
                Object other$spaceName = other.getSpaceName();
                if (this$spaceName == null) {
                    if (other$spaceName != null) {
                        return false;
                    }
                } else if (!this$spaceName.equals(other$spaceName)) {
                    return false;
                }
                Object this$description = getDescription();
                Object other$description = other.getDescription();
                if (this$description == null) {
                    if (other$description != null) {
                        return false;
                    }
                } else if (!this$description.equals(other$description)) {
                    return false;
                }
                Object this$belongOrgId = getBelongOrgId();
                Object other$belongOrgId = other.getBelongOrgId();
                if (this$belongOrgId == null) {
                    if (other$belongOrgId != null) {
                        return false;
                    }
                } else if (!this$belongOrgId.equals(other$belongOrgId)) {
                    return false;
                }
                Object this$sourceId = getSourceId();
                Object other$sourceId = other.getSourceId();
                if (this$sourceId == null) {
                    if (other$sourceId != null) {
                        return false;
                    }
                } else if (!this$sourceId.equals(other$sourceId)) {
                    return false;
                }
                Object this$queryParam = getQueryParam();
                Object other$queryParam = other.getQueryParam();
                if (this$queryParam == null) {
                    if (other$queryParam != null) {
                        return false;
                    }
                } else if (!this$queryParam.equals(other$queryParam)) {
                    return false;
                }
                Object this$appProjectId = getAppProjectId();
                Object other$appProjectId = other.getAppProjectId();
                if (this$appProjectId == null) {
                    if (other$appProjectId != null) {
                        return false;
                    }
                } else if (!this$appProjectId.equals(other$appProjectId)) {
                    return false;
                }
                Object this$startTime = getStartTime();
                Object other$startTime = other.getStartTime();
                if (this$startTime == null) {
                    if (other$startTime != null) {
                        return false;
                    }
                } else if (!this$startTime.equals(other$startTime)) {
                    return false;
                }
                Object this$endTime = getEndTime();
                Object other$endTime = other.getEndTime();
                if (this$endTime == null) {
                    if (other$endTime != null) {
                        return false;
                    }
                } else if (!this$endTime.equals(other$endTime)) {
                    return false;
                }
                Object this$createTime = getCreateTime();
                Object other$createTime = other.getCreateTime();
                if (this$createTime == null) {
                    if (other$createTime != null) {
                        return false;
                    }
                } else if (!this$createTime.equals(other$createTime)) {
                    return false;
                }
                Object this$updateTime = getUpdateTime();
                Object other$updateTime = other.getUpdateTime();
                return this$updateTime == null ? other$updateTime == null : this$updateTime.equals(other$updateTime);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ProjectV2;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $processDictionaryId = getProcessDictionaryId();
        int result2 = (result * 59) + ($processDictionaryId == null ? 43 : $processDictionaryId.hashCode());
        Object $typeDictionaryId = getTypeDictionaryId();
        int result3 = (result2 * 59) + ($typeDictionaryId == null ? 43 : $typeDictionaryId.hashCode());
        Object $sourceDictionaryId = getSourceDictionaryId();
        int result4 = (result3 * 59) + ($sourceDictionaryId == null ? 43 : $sourceDictionaryId.hashCode());
        Object $statusDictionaryId = getStatusDictionaryId();
        int result5 = (result4 * 59) + ($statusDictionaryId == null ? 43 : $statusDictionaryId.hashCode());
        Object $userIdList = getUserIdList();
        int result6 = (result5 * 59) + ($userIdList == null ? 43 : $userIdList.hashCode());
        Object $templateId = getTemplateId();
        int result7 = (result6 * 59) + ($templateId == null ? 43 : $templateId.hashCode());
        Object $edepId = getEdepId();
        int result8 = (result7 * 59) + ($edepId == null ? 43 : $edepId.hashCode());
        Object $tenantId = getTenantId();
        int result9 = (result8 * 59) + ($tenantId == null ? 43 : $tenantId.hashCode());
        Object $systemId = getSystemId();
        int result10 = (result9 * 59) + ($systemId == null ? 43 : $systemId.hashCode());
        Object $createBy = getCreateBy();
        int result11 = (result10 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateBy = getUpdateBy();
        int result12 = (result11 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $projectCode = getProjectCode();
        int result13 = (result12 * 59) + ($projectCode == null ? 43 : $projectCode.hashCode());
        Object $name = getName();
        int result14 = (result13 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $spaceName = getSpaceName();
        int result15 = (result14 * 59) + ($spaceName == null ? 43 : $spaceName.hashCode());
        Object $description = getDescription();
        int result16 = (result15 * 59) + ($description == null ? 43 : $description.hashCode());
        Object $belongOrgId = getBelongOrgId();
        int result17 = (result16 * 59) + ($belongOrgId == null ? 43 : $belongOrgId.hashCode());
        Object $sourceId = getSourceId();
        int result18 = (result17 * 59) + ($sourceId == null ? 43 : $sourceId.hashCode());
        Object $queryParam = getQueryParam();
        int result19 = (result18 * 59) + ($queryParam == null ? 43 : $queryParam.hashCode());
        Object $appProjectId = getAppProjectId();
        int result20 = (result19 * 59) + ($appProjectId == null ? 43 : $appProjectId.hashCode());
        Object $startTime = getStartTime();
        int result21 = (result20 * 59) + ($startTime == null ? 43 : $startTime.hashCode());
        Object $endTime = getEndTime();
        int result22 = (result21 * 59) + ($endTime == null ? 43 : $endTime.hashCode());
        Object $createTime = getCreateTime();
        int result23 = (result22 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        return (result23 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
    }

    public String toString() {
        return "ProjectV2(id=" + getId() + ", projectCode=" + getProjectCode() + ", name=" + getName() + ", spaceName=" + getSpaceName() + ", description=" + getDescription() + ", belongOrgId=" + getBelongOrgId() + ", processDictionaryId=" + getProcessDictionaryId() + ", typeDictionaryId=" + getTypeDictionaryId() + ", sourceId=" + getSourceId() + ", sourceDictionaryId=" + getSourceDictionaryId() + ", statusDictionaryId=" + getStatusDictionaryId() + ", queryParam=" + getQueryParam() + ", userIdList=" + getUserIdList() + ", templateId=" + getTemplateId() + ", edepId=" + getEdepId() + ", appProjectId=" + getAppProjectId() + ", startTime=" + getStartTime() + ", endTime=" + getEndTime() + ", tenantId=" + getTenantId() + ", systemId=" + getSystemId() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + ", createBy=" + getCreateBy() + ", updateBy=" + getUpdateBy() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public String getProjectCode() {
        return this.projectCode;
    }

    public String getName() {
        return this.name;
    }

    public String getSpaceName() {
        return this.spaceName;
    }

    public String getDescription() {
        return this.description;
    }

    public String getBelongOrgId() {
        return this.belongOrgId;
    }

    public Long getProcessDictionaryId() {
        return this.processDictionaryId;
    }

    public Long getTypeDictionaryId() {
        return this.typeDictionaryId;
    }

    public String getSourceId() {
        return this.sourceId;
    }

    public Long getSourceDictionaryId() {
        return this.sourceDictionaryId;
    }

    public Long getStatusDictionaryId() {
        return this.statusDictionaryId;
    }

    public String getQueryParam() {
        return this.queryParam;
    }

    public Long getUserIdList() {
        return this.userIdList;
    }

    public Long getTemplateId() {
        return this.templateId;
    }

    public Long getEdepId() {
        return this.edepId;
    }

    public String getAppProjectId() {
        return this.appProjectId;
    }

    public String getStartTime() {
        return this.startTime;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public Long getSystemId() {
        return this.systemId;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public String getUpdateTime() {
        return this.updateTime;
    }

    public Long getCreateBy() {
        return this.createBy;
    }

    public Long getUpdateBy() {
        return this.updateBy;
    }
}
