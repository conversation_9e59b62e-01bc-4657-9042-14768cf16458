package cn.harmonycloud.issue.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
/* loaded from: IssuesUpdateDTO.class */
public class IssuesUpdateDTO implements Serializable {
    private static final long serialVersionUID = -3029544113243011951L;
    @ApiModelProperty("事项类型id")
    private Long issuesTypeId;
    @ApiModelProperty("事项类型编码")
    private String issuesTypeCode;
    @ApiModelProperty("业务ID")
    private Long businessId;
    @ApiModelProperty(value = "业务类型", notes = "businessId+businessType唯一")
    private Integer businessType;
    @ApiModelProperty("父事项ID")
    private Long parentId;
    @ApiModelProperty("属性字段")
    private List<IssuesFieldDTO> customFields;

    public void setIssuesTypeId(final Long issuesTypeId) {
        this.issuesTypeId = issuesTypeId;
    }

    public void setIssuesTypeCode(final String issuesTypeCode) {
        this.issuesTypeCode = issuesTypeCode;
    }

    public void setBusinessId(final Long businessId) {
        this.businessId = businessId;
    }

    public void setBusinessType(final Integer businessType) {
        this.businessType = businessType;
    }

    public void setParentId(final Long parentId) {
        this.parentId = parentId;
    }

    public void setCustomFields(final List<IssuesFieldDTO> customFields) {
        this.customFields = customFields;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesUpdateDTO) {
            IssuesUpdateDTO other = (IssuesUpdateDTO) o;
            if (other.canEqual(this)) {
                Object this$issuesTypeId = getIssuesTypeId();
                Object other$issuesTypeId = other.getIssuesTypeId();
                if (this$issuesTypeId == null) {
                    if (other$issuesTypeId != null) {
                        return false;
                    }
                } else if (!this$issuesTypeId.equals(other$issuesTypeId)) {
                    return false;
                }
                Object this$businessId = getBusinessId();
                Object other$businessId = other.getBusinessId();
                if (this$businessId == null) {
                    if (other$businessId != null) {
                        return false;
                    }
                } else if (!this$businessId.equals(other$businessId)) {
                    return false;
                }
                Object this$businessType = getBusinessType();
                Object other$businessType = other.getBusinessType();
                if (this$businessType == null) {
                    if (other$businessType != null) {
                        return false;
                    }
                } else if (!this$businessType.equals(other$businessType)) {
                    return false;
                }
                Object this$parentId = getParentId();
                Object other$parentId = other.getParentId();
                if (this$parentId == null) {
                    if (other$parentId != null) {
                        return false;
                    }
                } else if (!this$parentId.equals(other$parentId)) {
                    return false;
                }
                Object this$issuesTypeCode = getIssuesTypeCode();
                Object other$issuesTypeCode = other.getIssuesTypeCode();
                if (this$issuesTypeCode == null) {
                    if (other$issuesTypeCode != null) {
                        return false;
                    }
                } else if (!this$issuesTypeCode.equals(other$issuesTypeCode)) {
                    return false;
                }
                Object this$customFields = getCustomFields();
                Object other$customFields = other.getCustomFields();
                return this$customFields == null ? other$customFields == null : this$customFields.equals(other$customFields);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesUpdateDTO;
    }

    public int hashCode() {
        Object $issuesTypeId = getIssuesTypeId();
        int result = (1 * 59) + ($issuesTypeId == null ? 43 : $issuesTypeId.hashCode());
        Object $businessId = getBusinessId();
        int result2 = (result * 59) + ($businessId == null ? 43 : $businessId.hashCode());
        Object $businessType = getBusinessType();
        int result3 = (result2 * 59) + ($businessType == null ? 43 : $businessType.hashCode());
        Object $parentId = getParentId();
        int result4 = (result3 * 59) + ($parentId == null ? 43 : $parentId.hashCode());
        Object $issuesTypeCode = getIssuesTypeCode();
        int result5 = (result4 * 59) + ($issuesTypeCode == null ? 43 : $issuesTypeCode.hashCode());
        Object $customFields = getCustomFields();
        return (result5 * 59) + ($customFields == null ? 43 : $customFields.hashCode());
    }

    public String toString() {
        return "IssuesUpdateDTO(issuesTypeId=" + getIssuesTypeId() + ", issuesTypeCode=" + getIssuesTypeCode() + ", businessId=" + getBusinessId() + ", businessType=" + getBusinessType() + ", parentId=" + getParentId() + ", customFields=" + getCustomFields() + ")";
    }

    public Long getIssuesTypeId() {
        return this.issuesTypeId;
    }

    public String getIssuesTypeCode() {
        return this.issuesTypeCode;
    }

    public Long getBusinessId() {
        return this.businessId;
    }

    public Integer getBusinessType() {
        return this.businessType;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public List<IssuesFieldDTO> getCustomFields() {
        return this.customFields;
    }
}
