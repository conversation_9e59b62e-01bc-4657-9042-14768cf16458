package cn.harmonycloud.issue.model.dto.v2;

/* loaded from: ProjectModuleDTO.class */
public class ProjectModuleDTO {
    private Long id;
    private Long projectId;
    private String name;
    private Long parentId;
    private Long icon;
    private String createTime;
    private String updateTime;
    private Long createBy;
    private Long updateBy;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectId(final Long projectId) {
        this.projectId = projectId;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setParentId(final Long parentId) {
        this.parentId = parentId;
    }

    public void setIcon(final Long icon) {
        this.icon = icon;
    }

    public void setCreateTime(final String createTime) {
        this.createTime = createTime;
    }

    public void setUpdateTime(final String updateTime) {
        this.updateTime = updateTime;
    }

    public void setCreateBy(final Long createBy) {
        this.createBy = createBy;
    }

    public void setUpdateBy(final Long updateBy) {
        this.updateBy = updateBy;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ProjectModuleDTO) {
            ProjectModuleDTO other = (ProjectModuleDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$projectId = getProjectId();
                Object other$projectId = other.getProjectId();
                if (this$projectId == null) {
                    if (other$projectId != null) {
                        return false;
                    }
                } else if (!this$projectId.equals(other$projectId)) {
                    return false;
                }
                Object this$parentId = getParentId();
                Object other$parentId = other.getParentId();
                if (this$parentId == null) {
                    if (other$parentId != null) {
                        return false;
                    }
                } else if (!this$parentId.equals(other$parentId)) {
                    return false;
                }
                Object this$icon = getIcon();
                Object other$icon = other.getIcon();
                if (this$icon == null) {
                    if (other$icon != null) {
                        return false;
                    }
                } else if (!this$icon.equals(other$icon)) {
                    return false;
                }
                Object this$createBy = getCreateBy();
                Object other$createBy = other.getCreateBy();
                if (this$createBy == null) {
                    if (other$createBy != null) {
                        return false;
                    }
                } else if (!this$createBy.equals(other$createBy)) {
                    return false;
                }
                Object this$updateBy = getUpdateBy();
                Object other$updateBy = other.getUpdateBy();
                if (this$updateBy == null) {
                    if (other$updateBy != null) {
                        return false;
                    }
                } else if (!this$updateBy.equals(other$updateBy)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$createTime = getCreateTime();
                Object other$createTime = other.getCreateTime();
                if (this$createTime == null) {
                    if (other$createTime != null) {
                        return false;
                    }
                } else if (!this$createTime.equals(other$createTime)) {
                    return false;
                }
                Object this$updateTime = getUpdateTime();
                Object other$updateTime = other.getUpdateTime();
                return this$updateTime == null ? other$updateTime == null : this$updateTime.equals(other$updateTime);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ProjectModuleDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $projectId = getProjectId();
        int result2 = (result * 59) + ($projectId == null ? 43 : $projectId.hashCode());
        Object $parentId = getParentId();
        int result3 = (result2 * 59) + ($parentId == null ? 43 : $parentId.hashCode());
        Object $icon = getIcon();
        int result4 = (result3 * 59) + ($icon == null ? 43 : $icon.hashCode());
        Object $createBy = getCreateBy();
        int result5 = (result4 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateBy = getUpdateBy();
        int result6 = (result5 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $name = getName();
        int result7 = (result6 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $createTime = getCreateTime();
        int result8 = (result7 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        return (result8 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
    }

    public String toString() {
        return "ProjectModuleDTO(id=" + getId() + ", projectId=" + getProjectId() + ", name=" + getName() + ", parentId=" + getParentId() + ", icon=" + getIcon() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + ", createBy=" + getCreateBy() + ", updateBy=" + getUpdateBy() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public Long getProjectId() {
        return this.projectId;
    }

    public String getName() {
        return this.name;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public Long getIcon() {
        return this.icon;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public String getUpdateTime() {
        return this.updateTime;
    }

    public Long getCreateBy() {
        return this.createBy;
    }

    public Long getUpdateBy() {
        return this.updateBy;
    }
}
