package cn.harmonycloud.issue.model.dto.v2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;

/* loaded from: ProjectByIdsVO.class */
public class ProjectByIdsVO {
    private static final long serialVersionUID = 1;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(name = "项目名称")
    private String name;
    @ApiModelProperty(name = "空间名称")
    private String spaceName;
    @ApiModelProperty(name = "归属组织id")
    private Long tenantId;
    @ApiModelProperty(name = "模版ID")
    private Long templateId;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setSpaceName(final String spaceName) {
        this.spaceName = spaceName;
    }

    public void setTenantId(final Long tenantId) {
        this.tenantId = tenantId;
    }

    public void setTemplateId(final Long templateId) {
        this.templateId = templateId;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ProjectByIdsVO) {
            ProjectByIdsVO other = (ProjectByIdsVO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$tenantId = getTenantId();
                Object other$tenantId = other.getTenantId();
                if (this$tenantId == null) {
                    if (other$tenantId != null) {
                        return false;
                    }
                } else if (!this$tenantId.equals(other$tenantId)) {
                    return false;
                }
                Object this$templateId = getTemplateId();
                Object other$templateId = other.getTemplateId();
                if (this$templateId == null) {
                    if (other$templateId != null) {
                        return false;
                    }
                } else if (!this$templateId.equals(other$templateId)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$spaceName = getSpaceName();
                Object other$spaceName = other.getSpaceName();
                return this$spaceName == null ? other$spaceName == null : this$spaceName.equals(other$spaceName);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ProjectByIdsVO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $tenantId = getTenantId();
        int result2 = (result * 59) + ($tenantId == null ? 43 : $tenantId.hashCode());
        Object $templateId = getTemplateId();
        int result3 = (result2 * 59) + ($templateId == null ? 43 : $templateId.hashCode());
        Object $name = getName();
        int result4 = (result3 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $spaceName = getSpaceName();
        return (result4 * 59) + ($spaceName == null ? 43 : $spaceName.hashCode());
    }

    public String toString() {
        return "ProjectByIdsVO(id=" + getId() + ", name=" + getName() + ", spaceName=" + getSpaceName() + ", tenantId=" + getTenantId() + ", templateId=" + getTemplateId() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public String getSpaceName() {
        return this.spaceName;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public Long getTemplateId() {
        return this.templateId;
    }
}
