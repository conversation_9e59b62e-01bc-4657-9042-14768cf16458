package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

@ApiModel
/* loaded from: IssuesTypeStatusDTO.class */
public class IssuesTypeStatusDTO implements Serializable {
    @ApiModelProperty("主键ID")
    private Long id;
    @ApiModelProperty("事项类型ID")
    private Long issuesTypeId;
    @ApiModelProperty("事项状态ID")
    private Long statusId;
    @ApiModelProperty("状态阶段")
    private Integer statusType;
    @ApiModelProperty("事项状态name")
    private String name;
    @ApiModelProperty("排序字段")
    private Integer position;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setIssuesTypeId(final Long issuesTypeId) {
        this.issuesTypeId = issuesTypeId;
    }

    public void setStatusId(final Long statusId) {
        this.statusId = statusId;
    }

    public void setStatusType(final Integer statusType) {
        this.statusType = statusType;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setPosition(final Integer position) {
        this.position = position;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesTypeStatusDTO) {
            IssuesTypeStatusDTO other = (IssuesTypeStatusDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$issuesTypeId = getIssuesTypeId();
                Object other$issuesTypeId = other.getIssuesTypeId();
                if (this$issuesTypeId == null) {
                    if (other$issuesTypeId != null) {
                        return false;
                    }
                } else if (!this$issuesTypeId.equals(other$issuesTypeId)) {
                    return false;
                }
                Object this$statusId = getStatusId();
                Object other$statusId = other.getStatusId();
                if (this$statusId == null) {
                    if (other$statusId != null) {
                        return false;
                    }
                } else if (!this$statusId.equals(other$statusId)) {
                    return false;
                }
                Object this$statusType = getStatusType();
                Object other$statusType = other.getStatusType();
                if (this$statusType == null) {
                    if (other$statusType != null) {
                        return false;
                    }
                } else if (!this$statusType.equals(other$statusType)) {
                    return false;
                }
                Object this$position = getPosition();
                Object other$position = other.getPosition();
                if (this$position == null) {
                    if (other$position != null) {
                        return false;
                    }
                } else if (!this$position.equals(other$position)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                return this$name == null ? other$name == null : this$name.equals(other$name);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesTypeStatusDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $issuesTypeId = getIssuesTypeId();
        int result2 = (result * 59) + ($issuesTypeId == null ? 43 : $issuesTypeId.hashCode());
        Object $statusId = getStatusId();
        int result3 = (result2 * 59) + ($statusId == null ? 43 : $statusId.hashCode());
        Object $statusType = getStatusType();
        int result4 = (result3 * 59) + ($statusType == null ? 43 : $statusType.hashCode());
        Object $position = getPosition();
        int result5 = (result4 * 59) + ($position == null ? 43 : $position.hashCode());
        Object $name = getName();
        return (result5 * 59) + ($name == null ? 43 : $name.hashCode());
    }

    public String toString() {
        return "IssuesTypeStatusDTO(id=" + getId() + ", issuesTypeId=" + getIssuesTypeId() + ", statusId=" + getStatusId() + ", statusType=" + getStatusType() + ", name=" + getName() + ", position=" + getPosition() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public Long getIssuesTypeId() {
        return this.issuesTypeId;
    }

    public Long getStatusId() {
        return this.statusId;
    }

    public Integer getStatusType() {
        return this.statusType;
    }

    public String getName() {
        return this.name;
    }

    public Integer getPosition() {
        return this.position;
    }
}
