package cn.harmonycloud.issue.enums;

import java.util.Set;
import org.apache.commons.compress.utils.Sets;

/* loaded from: IssuesRelationTypeEnum.class */
public enum IssuesRelationTypeEnum {
    IssuesRelationType(99L, "issuesRelationType", "事项关联类型", 0),
    RELATE(100L, "relate", "关联", 1),
    DUPLICATE(101L, "duplicate", "复制", 0),
    PARENT(107L, "parent", "父级", 0),
    CHANGE(128L, "change", "变更", 0),
    MAN_HOURS(132L, "manHours", "工时", 0),
    SHARE(129L, "share", "共享", 0),
    EXT_RELATE(108L, "extRelate", "外部关联", 0);
    
    private Long id;
    private String code;
    private String name;
    private Integer isDefault;

    IssuesRelationTypeEnum(final Long id, final String code, final String name, final Integer isDefault) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.isDefault = isDefault;
    }

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getIsDefault() {
        return this.isDefault;
    }

    public static IssuesRelationTypeEnum getDefaultRelationType() {
        IssuesRelationTypeEnum[] values;
        for (IssuesRelationTypeEnum issuesRelationTypeEnum : values()) {
            if (issuesRelationTypeEnum.isDefault.intValue() == 1) {
                return issuesRelationTypeEnum;
            }
        }
        return null;
    }

    public static Set<String> getNotDisplayRelationTypeCode() {
        return Sets.newHashSet(new String[]{DUPLICATE.getCode(), PARENT.getCode(), CHANGE.getCode(), EXT_RELATE.getCode(), MAN_HOURS.getCode()});
    }

    public static Set<Long> getNotDelRelationType() {
        return Sets.newHashSet(new Long[]{PARENT.getId(), CHANGE.getId(), EXT_RELATE.getId(), MAN_HOURS.getId()});
    }
}
