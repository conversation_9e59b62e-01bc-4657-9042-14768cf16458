package cn.harmonycloud.issue.provider;

import cn.harmonycloud.common.core.base.BaseResult;

import cn.harmonycloud.issue.model.dto.v2.ProjectByIdsVO;
import cn.harmonycloud.issue.model.dto.v2.ProjectQueryDTO;
import cn.harmonycloud.issue.model.dto.v2.ProjectV2;
import cn.harmonycloud.issue.model.vo.v2.ProjectBaseVO;
import cn.harmonycloud.issue.model.vo.v2.ProjectQueryVO;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "projectProviderV2", path = "/provider/devops/project/v2", url = "${track-issues.url:http://localhost:8081/}")
/* loaded from: ProjectProviderV2.class */
public interface ProjectProviderV2 {
    @GetMapping({"/queryAll"})
    @ApiOperation("查询所有项目")
    BaseResult<List<ProjectQueryVO>> queryAll(@SpringQueryMap ProjectQueryDTO projectQueryDTO);

    @GetMapping({"/{id}"})
    @ApiOperation("根据ID查询项目详情")
    BaseResult<ProjectQueryVO> getById(@PathVariable String id);

    @GetMapping({"/ids"})
    @ApiOperation(value = "根据ids查询map<id,name>", consumes = "application/json")
    BaseResult<List<ProjectByIdsVO>> getByIds(@RequestParam List<Long> projectIds);

    @GetMapping({"/queryBaseList"})
    @ApiOperation("查询所有项目的基本信息")
    BaseResult<List<ProjectQueryVO>> queryBaseList(@SpringQueryMap ProjectQueryDTO projectQueryDTO);

    @GetMapping({"/user"})
    @ApiOperation("模糊查询")
    BaseResult<List<ProjectBaseVO>> getByUser();

    @GetMapping
    @ApiOperation("获取全量项目")
    BaseResult<List<ProjectV2>> getList();
}
