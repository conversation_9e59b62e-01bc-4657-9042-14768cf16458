package cn.harmonycloud.trinasolar.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

import java.io.Serializable;

/**
 * JSON 返回主体信息封装
 * <p>
 * 支持带分页信息，支持泛型，支持HttpStatus标准返回码
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2019-07-19 09:34
 */
@Getter
@Setter
@ToString
@ApiModel(value = "统一返回结果")
public class R<T> implements Serializable {
    private static final long serialVersionUID = -5440768645471506852L;

    @ApiModelProperty(value = "状态码")
    private int code;

    @ApiModelProperty(value = "提示信息")
    private String message;

    @ApiModelProperty(value = "数据")
    private T data;

    private R() {
        this.code = ResponseEnum.SUCCESS.getCode();
        this.message = ResponseEnum.SUCCESS.getMessage();
    }

    private R(int code, String message) {
        this.code = code;
        this.message = message;
    }

    private R(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    @JSONField(serialize = false) // 避免序列化
    public boolean isSuccess() {
        return code == ResponseEnum.SUCCESS.getCode();
    }

    public static <T> R<T> success() {
        return new R<>();
    }

    // 快速返回成功
    public static <T> R<T> success(ResponseEnum responseEnum) {
        return new R<>(responseEnum.getCode(), responseEnum.getMessage());
    }

    public static <T> R<T> success(T result) {
        return new R<>(ResponseEnum.SUCCESS.getCode(), ResponseEnum.SUCCESS.getMessage(), result);
    }

    public static <T> R<T> success(ResponseEnum responseEnum, T result) {
        return new R<>(responseEnum.getCode(), responseEnum.getMessage(), result);
    }

    //快速返回失败状态
    public static <T> R<T> fail() {
        return new R<>(ResponseEnum.FAILED.getCode(), ResponseEnum.FAILED.getMessage());
    }

    public static <T> R<T> fail(ResponseEnum responseEnum) {
        return new R<>(responseEnum.getCode(), responseEnum.getMessage());
    }

    public static <T> R<T> fail(int code, String message) {
        return new R<>(code, message);
    }

    public static <T> R<T> fail(T result) {
        return new R<>(ResponseEnum.FAILED.getCode(), ResponseEnum.FAILED.getMessage(), result);
    }

    public static <T> R<T> fail(ResponseEnum responseEnum, T result) {
        return new R<>(responseEnum.getCode(), responseEnum.getMessage(), result);
    }

    //快速返回自定义状态码
    public static <T> R<T> result(int code, String message) {
        return new R<>(code, message);
    }

    public static <T> R<T> result(int code, String message, T result) {
        return new R<>(code, message, result);
    }

    //快速返回Http状态
    public static <T> R<T> httpStatus(HttpStatus httpStatus) {
        return new R<>(httpStatus.value(), httpStatus.getReasonPhrase());
    }

    public static <T> R<T> httpStatus(HttpStatus httpStatus, String message) {
        return new R<>(httpStatus.value(), message);
    }

    public static <T> R<T> httpStatus(HttpStatus httpStatus, String message, T result) {
        return new R<>(httpStatus.value(), message, result);
    }

}
