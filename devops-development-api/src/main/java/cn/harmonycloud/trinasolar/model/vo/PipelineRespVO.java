package cn.harmonycloud.trinasolar.model.vo;

import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class PipelineRespVO {
    /**
     * 流水线ID
     */
    private String pipelineId;
    /**
     * 应用程序ID
     */
    private Long programId;
    /**
     * 流水线名称
     */
    private String pipelineName;

    private String envName;

    /**
     * 运行状态(SUCCEED FAILED  CANCELED RUNNING UNKNOWN)
     */
    private String latestBuildStatus;

    /**
     * 上次运行时间
     */
    private String latestBuildStartTime;

    /**
     * 执行人
     */
    private String buildUser;
    private String latestBuildId;
    private String latestBuildEndTime;
    private int latestBuildNum;
    private String lastBuildMsg;
    private int buildCount;
    private String createTime;
    private String updateTime;

    /**
     * 项目编码
     */
    private String projectCode;

}
