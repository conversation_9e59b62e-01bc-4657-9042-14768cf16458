package cn.harmonycloud.enums;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/8/2
 */
@AllArgsConstructor
public enum SystemEnum {
    /**
     * 系统
     */
    SYSTEM("system", "系统"),

    /**
     * 子系统
     */
    SUB_SYSTEM("subSystem", "子系统");

    private String code;

    private String name;

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
