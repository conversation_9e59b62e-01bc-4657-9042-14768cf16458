package cn.harmonycloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022/9/6
 */
@Getter
@AllArgsConstructor
public enum ProductTypeEnum {
    ZIP("zip", "压缩包", "01"),
    TAR_GZ("tar.gz", "tar包", "01"),
    YAML("yaml", "yaml", "02");
    private String name;
    private String desc;
    private String type;

    public static String getProductType(String name) {
        for (ProductTypeEnum value : ProductTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getType();
            }
        }
        return null;
    }
}
