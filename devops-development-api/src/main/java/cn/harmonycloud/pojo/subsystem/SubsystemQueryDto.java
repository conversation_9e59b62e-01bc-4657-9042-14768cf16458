package cn.harmonycloud.pojo.subsystem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/4 10:27 上午
 **/
@ApiModel("子系统查询参数")
public class SubsystemQueryDto {

    @ApiModelProperty("系统id集合")
    private List<Long> systemIds;

    @ApiModelProperty("子系统id集合")
    private List<Long> subsystemIds;

    public List<Long> getSystemIds() {
        return systemIds;
    }

    public void setSystemIds(List<Long> systemIds) {
        this.systemIds = systemIds;
    }

    public List<Long> getSubsystemIds() {
        return subsystemIds;
    }

    public void setSubsystemIds(List<Long> subsystemIds) {
        this.subsystemIds = subsystemIds;
    }
}
