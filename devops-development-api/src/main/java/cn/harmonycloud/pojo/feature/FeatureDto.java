package cn.harmonycloud.pojo.feature;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/23 7:17 下午
 **/
@ApiModel("特性信息实体")
public class FeatureDto implements Serializable {

    private static final long serialVersionUID = -8432390379021852127L;

    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("特性名称")
    private String featureName;
    @ApiModelProperty("特性编码")
    private String featureCode;
    @ApiModelProperty("特性类型：0-特性 1-缺陷")
    private Integer featureType;
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("系统id")
    private Long systemId;
    @ApiModelProperty("子系统id")
    private Long subSystemId;
    @ApiModelProperty("负责人id")
    private Long director;
    @ApiModelProperty("特性状态：0-开发中，1-开发完成，2-测试中，3-已发布")
    private Integer featureStatus;
    @ApiModelProperty("预计完成时间")
    private String completeTime;
}
