package cn.harmonycloud.pmp.util;

import cn.harmonycloud.common.core.constant.CommonConstants;
import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.pmp.Enum.MethodEnum;
import cn.harmonycloud.pmp.Enum.ParamTypeEnum;
import cn.harmonycloud.pmp.model.entity.AssertResult;
import cn.harmonycloud.pmp.model.entity.BaseResult;
import cn.harmonycloud.pmp.model.entity.Param;
import cn.harmonycloud.pmp.model.entity.ServerConfig;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class HttpRequestUtil {
    private static final CloseableHttpClient httpClient;

    static {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setMaxTotal(100);
        cm.setDefaultMaxPerRoute(20);
        cm.setDefaultMaxPerRoute(50);
        httpClient = HttpClients.custom().setConnectionManager(cm).build();
    }

    public static String get(String url, Map<String,Object> headerMap) {
        CloseableHttpResponse response = null;
        BufferedReader in = null;
        String result = "";
        try {
            HttpGet httpGet = new HttpGet(url);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(30000).setConnectionRequestTimeout(30000).setSocketTimeout(30000).build();
            httpGet.setConfig(requestConfig);
            httpGet.setConfig(requestConfig);
            httpGet.addHeader("Content-type", "application/json; charset=utf-8");
            httpGet.setHeader("Accept", "application/json");
            if(CollUtil.isNotEmpty(headerMap)){
                for(Map.Entry<String,Object> entry:headerMap.entrySet()){
                    httpGet.addHeader(entry.getKey(), String.valueOf(entry.getValue()));
                }
            }
            response = httpClient.execute(httpGet);
            in = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
            StringBuffer sb = new StringBuffer();
            String line = "";
            String NL = System.getProperty("line.separator");
            while ((line = in.readLine()) != null) {
                sb.append(line + NL);
            }
            in.close();
            result = sb.toString();
            BaseResult baseResult = JSON.parseObject(result, BaseResult.class);
            if (baseResult.getCode() != CommonConstants.SUCCESS) {
                throw new BusinessException("调用接口失败" + result);
            }
            result = baseResult.getData().toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != response) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    private static String getUrlByParamMap(Map<String,Object> map, String pureUrl) {
        StringBuilder queryStringBuilder = new StringBuilder(pureUrl);
        queryStringBuilder.append("?");
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if(ObjectUtil.isNotNull(entry.getValue())){
                queryStringBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        return queryStringBuilder.toString().replaceFirst("&$", "");
    }

    public static String get(String url,Map<String,Object> paramMap, Map<String,Object> headerMap) {
        CloseableHttpResponse response = null;
        BufferedReader in = null;
        String result = "";
        try {
            if(CollUtil.isNotEmpty(paramMap)){
                url = getUrlByParamMap(paramMap,url);
            }
            HttpGet httpGet = new HttpGet(url);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(30000).setConnectionRequestTimeout(30000).setSocketTimeout(30000).build();
            httpGet.setConfig(requestConfig);
            httpGet.setConfig(requestConfig);
            httpGet.addHeader("Content-type", "application/json; charset=utf-8");
            httpGet.setHeader("Accept", "application/json");
            if(CollUtil.isNotEmpty(headerMap)){
                for(Map.Entry<String,Object> entry:headerMap.entrySet()){
                    httpGet.addHeader(entry.getKey(), String.valueOf(entry.getValue()));
                }
            }
            response = httpClient.execute(httpGet);
            in = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
            StringBuffer sb = new StringBuffer();
            String line = "";
            String NL = System.getProperty("line.separator");
            while ((line = in.readLine()) != null) {
                sb.append(line + NL);
            }
            in.close();
            result = sb.toString();
            BaseResult baseResult = JSON.parseObject(result, BaseResult.class);
            if (baseResult.getCode() != CommonConstants.SUCCESS) {
                throw new BusinessException("调用接口失败" + result);
            }
            result = baseResult.getData().toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != response) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static String post(String url, String jsonString,Map<String,Object> headerMap) {
        CloseableHttpResponse response = null;
        BufferedReader in;
        String result = "";
        try {
            HttpPost httpPost = new HttpPost(url);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(30000).setConnectionRequestTimeout(30000).setSocketTimeout(30000).build();
            httpPost.setConfig(requestConfig);
            httpPost.addHeader("Content-type", "application/json; charset=utf-8");
            httpPost.setHeader("Accept", "application/json");
            if(CollUtil.isNotEmpty(headerMap)){
                for(Map.Entry<String,Object> entry:headerMap.entrySet()){
                    httpPost.addHeader(entry.getKey(), String.valueOf(entry.getValue()));
                }
            }
            httpPost.setEntity(new StringEntity(jsonString, StandardCharsets.UTF_8));
            response = httpClient.execute(httpPost);
            in = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
            StringBuffer sb = new StringBuffer();
            String line = "";
            String NL = System.getProperty("line.separator");
            while ((line = in.readLine()) != null) {
                sb.append(line + NL);
            }
            in.close();
            result = sb.toString();
            BaseResult baseResult = JSON.parseObject(result, BaseResult.class);
            if(baseResult.getCode() != CommonConstants.SUCCESS.intValue() ) {
                throw new BusinessException("调用接口失败" + result);
            }
            if(ObjectUtil.isNotNull(baseResult.getData())){
                result = baseResult.getData().toString();
            }else{
                result = "";
            }
        } catch (Exception e) {
            log.error("调用接口失败",e);
            StringWriter stringWriter = new StringWriter();
            PrintWriter writer = new PrintWriter(stringWriter);
            e.printStackTrace(writer);
            StringBuffer buffer = stringWriter.getBuffer();
            result = buffer.toString();
        } finally {
            try {
                if (null != response) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static String getResultByPost(String url, String jsonString,Map<String,Object> headerMap) {
        CloseableHttpResponse response = null;
        BufferedReader in;
        String result = "";
        try {
            HttpPost httpPost = new HttpPost(url);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(30000).setConnectionRequestTimeout(30000).setSocketTimeout(30000).build();
            httpPost.setConfig(requestConfig);
            httpPost.addHeader("Content-type", "application/json; charset=utf-8");
            httpPost.setHeader("Accept", "application/json");
            if(CollUtil.isNotEmpty(headerMap)){
                for(Map.Entry<String,Object> entry:headerMap.entrySet()){
                    httpPost.addHeader(entry.getKey(), String.valueOf(entry.getValue()));
                }
            }
            httpPost.setEntity(new StringEntity(jsonString, StandardCharsets.UTF_8));
            response = httpClient.execute(httpPost);
            in = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
            StringBuffer sb = new StringBuffer();
            String line = "";
            String NL = System.getProperty("line.separator");
            while ((line = in.readLine()) != null) {
                sb.append(line + NL);
            }
            in.close();
            result = sb.toString();
        } catch (Exception e) {
            log.error("调用接口失败",e);
            StringWriter stringWriter = new StringWriter();
            PrintWriter writer = new PrintWriter(stringWriter);
            e.printStackTrace(writer);
            StringBuffer buffer = stringWriter.getBuffer();
            result = buffer.toString();
        } finally {
            try {
                if (null != response) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 根据api发送消息
     * @param serverConfig
     * @param valueMap
     * @return
     */
    public static AssertResult sendApi(ServerConfig serverConfig,Map<String,Object> valueMap){
        //组装参数
        Map<String,Object> paramMap = getParam(serverConfig,valueMap);
        Map<String,Object> headerMap = (Map<String, Object>)paramMap.get("headerMap");
        String resultJson="";
        if(MethodEnum.Get.getValue()==serverConfig.getMethod()){
            resultJson = get(serverConfig.getUrl(),headerMap );
        }else if(MethodEnum.Post.getValue()==serverConfig.getMethod()){
            String bodyJson=(String)paramMap.get("bodyJson");
            resultJson = post(serverConfig.getUrl(), bodyJson,headerMap);
        }
        if(StrUtil.isBlank(resultJson)){
            throw new BusinessException("发送短信接口返回异常");
        }
        return assertResult(serverConfig,resultJson);
    }

    /**
     * 组装参数
     * @param serverConfig
     * @param valueMap
     * @return
     */
    public static Map<String,Object> getParam(ServerConfig serverConfig,Map<String,Object> valueMap){
        String paramJson = serverConfig.getParam();
        if(StrUtil.isBlank(paramJson)){
            return new HashMap<>();
        }
        Map<String,Object> result = new HashMap<>();
        String url = serverConfig.getUrl();
        List<Param> params = JSON.parseArray(paramJson,Param.class);
        Map<String,Object> bodyMap = new HashMap<>();
        Map<String,Object> headerMap = new HashMap<>();
        Map<String,Object> queryMap = new HashMap<>();
        for(Param param : params){
            String key = param.getKey();
            if(ParamTypeEnum.Path.getCode().equals(param.getParamType())){
                //{key} 在路径上替换
                url=url.replace("{"+key+"}",(String)valueMap.get(key));
            }else if(ParamTypeEnum.Body.getCode().equals(param.getParamType())){
                bodyMap.put(key,valueMap.get(key));
            }else if(ParamTypeEnum.Header.getCode().equals(param.getParamType())){
                headerMap.put(key,valueMap.get(key));
            }else{
                queryMap.put(key,valueMap.get(key));
            }
        }
        if(CollUtil.isNotEmpty(queryMap)){
            url= url+"?"+URLUtil.buildQuery(queryMap,Charset.defaultCharset());
        }
        serverConfig.setUrl(url);
        result.put("bodyJson",JSON.toJSONString(bodyMap));
        result.put("headerMap",headerMap);
        return result;
    }

    /**
     * 断言
     * @param serverConfig
     * @return
     */
    public static AssertResult assertResult(ServerConfig serverConfig,String resultJson){
        String extractExpression = serverConfig.getExtractExpression();
        String successValue = serverConfig.getSuccessValue();
        Object assertValue = JsonPath.read(resultJson,extractExpression);
        if(ObjectUtil.isNull(assertValue)){
            return AssertResult.failed();
        }
        //此需求场景下只需要断言 相等的这种情况，不做其他扩展
        if(successValue.compareTo(assertValue.toString())==0){
            return AssertResult.successed();
        }
        return AssertResult.failed();
    }

//    /**
//     * 测试方法
//     * @param args
//     */
//    public static void main(String[] args) {
//        ServerConfig serverConfig = new ServerConfig();
//        List<Param> params = CollUtil.newArrayList();
//        Param param = new Param();
//        param.setParamType(ParamTypeEnum.Body.getCode());
//        param.setKey("username");
//        param.setComment("登录用户名");
//        params.add(param);
//
//        Param param1 = new Param();
//        param1.setParamType(ParamTypeEnum.Body.getCode());
//        param1.setKey("password");
//        param1.setComment("密码");
//        params.add(param1);
//
//        String paramJson = JSON.toJSONString(params);
//
//        String username="admin";
//        String password="12345678";
//        Map<String,String> valueMap = new HashMap<>();
//        valueMap.put("username",username);
//        valueMap.put("password",password);
//
//
//        serverConfig.setUrl("http://localhost:8080/users/noCode/login");
//        serverConfig.setExtractExpression("$.code");
//        serverConfig.setSuccessValue("0");
//        serverConfig.setParam(paramJson);
//        serverConfig.setMethod(MethodEnum.Post.getValue());
//
//        AssertResult assertResult = HttpRequestUtil.sendApi(serverConfig,valueMap);
//        System.out.println(assertResult.getCode());
//    }
}
