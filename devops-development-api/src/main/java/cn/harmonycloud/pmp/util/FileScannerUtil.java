package cn.harmonycloud.pmp.util;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.pmp.model.ExcelResult;
import cn.hutool.core.util.ObjectUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: liwei
 * @date 2022/1/18 16:35
 * @description
 */
public class FileScannerUtil {

    private static final Logger logger = LoggerFactory.getLogger("FileScannerUtil");

    /**
     * 解析excel包括xls 和xlsx
     **/

    public static Map<String,List> getxExcel(File file) {
        Map<String,List> result = new HashMap<>();
        List<String> setNameList = new ArrayList<>();
        List<ExcelResult> allList = new ArrayList<>();
        List<ExcelResult> failList = new ArrayList<>();
        //检查文件是否为空
        checkExcelFile(file);
        //获得workbook工作簿对象，判断是xls还是xlsx
        Workbook workbook = getWorkBook(file);
        if(ObjectUtil.isNotNull(workbook)){
            //开始循环sheet页，从0开始   workbook.getNumberOfSheets()获取页数
            for(int sheetIndex = 0; sheetIndex<workbook.getNumberOfSheets(); sheetIndex++){
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                if(ObjectUtil.isNull(sheet)){
                    continue;
                }
                //获得当前sheet结束行
                int lastRowNum = sheet.getLastRowNum();
                //获取第一行形成对象键
                int defrowNum;
                if(null == sheet.getRow(0).getCell(1)){
                    defrowNum = 3;
                }else{
                    defrowNum = 1;
                }

                //循环第一行外的所有行
                for(int rowNum = defrowNum; rowNum<=lastRowNum; rowNum++){
                    //获得当前行
                    Row row = sheet.getRow(rowNum);
                }

            }
        }
        result.put("set", setNameList);
        result.put("all", allList);
        result.put("fail", failList);
        return result;
    }


    /**
     * 检查文件是否存在以及是否为Excel
     **/

    public static void checkExcelFile(File file) {
        //判断文件是否存在
        if(null == file){
            throw new BusinessException("文件不存在");
        }

        //获得文件名
        String fileName = file.getName();
        System.out.println(fileName);
        //判断文件是否为excel
        if(!fileName.endsWith("xls") && !fileName.endsWith("xlsx")){
            throw new BusinessException(fileName + "不是excel文件");
        }
    }

    /**
     * 检查文件是否为txt
     **/
    public static void checkCsvFile(File file) throws IOException {
        //判断文件是否存在
        if(null == file){
            throw new BusinessException("文件不存在");
        }

        //获得文件名
        String fileName = file.getName();
        //判断文件是否为excel
        if(!fileName.endsWith("csv")){
            throw new BusinessException(fileName + "不是csv文件");
        }
    }

    /**
     *
     **/
    public static Workbook getWorkBook(File file) {
        //获取文件名称
        String fileName = file.getName();
        Workbook workbook = null;
        try{
            //获取excel文件的IO流
            FileInputStream inputStream = new FileInputStream(file);
            if(fileName.endsWith("xls")){
                workbook = new HSSFWorkbook(inputStream);
            }else if(fileName.endsWith("xlsx")){
                workbook = new XSSFWorkbook(inputStream);
            }
        }catch(IOException e){
            throw new BusinessException("");
        }
        return workbook;
    }
}
