package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.OperationAuditDTO;
import cn.harmonycloud.pmp.model.entity.OperationAuditPO;
import cn.harmonycloud.pmp.model.vo.OperationAuditVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OperationAuditConvert {

    OperationAuditConvert INSTANCE = Mappers.getMapper(OperationAuditConvert.class);

    OperationAuditPO OperationAuditDTO2PO(OperationAuditDTO dto);

    OperationAuditVO OperationAuditPO2VO(OperationAuditPO po);

}
