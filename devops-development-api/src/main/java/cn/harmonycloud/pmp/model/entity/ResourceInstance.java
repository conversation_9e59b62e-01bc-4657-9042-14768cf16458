package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import net.minidev.json.annotate.JsonIgnore;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 资源实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_resource_instance")
@ApiModel(value="ResourceInstance对象", description="资源实例表")
public class ResourceInstance implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonIgnore
    private Long id;

    @ApiModelProperty(value = "租户ID")
    @TableField(value = "organ_id")
    private Long organId;

    @ApiModelProperty(value = "父类id")
    @TableField(value = "parent_id")
    private Long parentId;

    @ApiModelProperty(value = "资源类型编号")
    @TableField("resource_type_code")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    @TableField("resource_instance_id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "应用资源名称")
    @TableField("resource_instance_name")
    private String resourceInstanceName;

    @ApiModelProperty(value = "排序号")
    @TableField("sri_sort")
    @JsonIgnore
    private Integer sort;

    @ApiModelProperty(value = "备注")
    @TableField("sri_description")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    @TableField("extend_field")
    private String extendField;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("is_deleted")
    @TableLogic
    @JsonIgnore
    private Boolean bolDeleted;

}
