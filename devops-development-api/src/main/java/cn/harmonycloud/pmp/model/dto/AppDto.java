package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "应用复合参数对象")
public class AppDto  implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "编号")
    @NotBlank(message = "编号不能为空")
    private String code;

    @ApiModelProperty(value = "应用首页地址")
    @NotBlank(message = "应用首页地址不能为空")
    private String url;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "左侧栏图标")
    private String leftIcon;

    @ApiModelProperty(value = "排序号")
    private Integer sortId;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "子应用菜单是否由主框架控制 0否 1是")
    private Integer frameControl;
}
