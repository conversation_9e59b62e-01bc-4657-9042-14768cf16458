package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.constant.AmpConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "用户组新增/删除表单对象")
public class UserGroupIdDto implements Serializable {

    @ApiModelProperty(value = "用户Id集合")
    private List<Long> userIdList;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户组Id")
    @NotNull(message = "用户组id不得为空")
    private Long userGroupId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "资源id",hidden = true)
    private Long resourceId= AmpConstant.ORGAN_RESOURCE_ID;

    @ApiModelProperty(value = "资源类型编号")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;


}
