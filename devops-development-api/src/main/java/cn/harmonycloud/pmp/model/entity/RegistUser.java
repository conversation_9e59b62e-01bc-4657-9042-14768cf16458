package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

@Data
public class RegistUser extends GateWayUser {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "email")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$")
    private String email;

    @ApiModelProperty(value = "密码")
    //@Pattern(regexp = "^.*(?=.{6,})(?=.*\\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*? ]).*$",message = "密码未符合规范")
    private String password;

    @ApiModelProperty(value = "用户类型 1-银行内部人员 2-金融客户 3-第三方开发者")
    private Integer loginUserType;

    @ApiModelProperty(value = "验证码随机串")
    private String randomStr;

    @ApiModelProperty(value = "验证码")
    private String authCode;

    @ApiModelProperty(value = "租户编码")
    private String orgCode;
}
