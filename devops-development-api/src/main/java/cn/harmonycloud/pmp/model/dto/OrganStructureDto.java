package cn.harmonycloud.pmp.model.dto;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class OrganStructureDto {

    private Long organId;

    private String organName;

    private List<ProjectStructureDto> projectList;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    public static class ProjectStructureDto {

        private Long projectId;

        private String projectName;

        private List<UserDto> userList;
    }
}
