package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 租户表
 * </p>
 * 用于返回
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
public class OrganizationTree implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称")
   private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "父类id")
    private Long parentId;
}
