package cn.harmonycloud.pmp.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: RecordVO
 * @projectName src-message-svc
 * @date 2020/11/4 14:39
 */
@Data
public class RecordVO {
    /**
     * 消息id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 消息类型（SMS/MAIL）
     */
    private Integer sendType;
    /**
     * 消息状态（NOT SEND/SUCCESS/FAILED）
     */
    private Integer status;
    /**
     * 收件人租户id 多个以，分割
     */
    private String tenantId;

    /**
     * 收件人租户名称
     */
    private String tenantName;

    /**
     * 收件人角色id 多个以，分割
     */
    private String roleIds;
    /**
     * 发送者名称
     */
    private String senderName;
    /**
     * 发送机制（DELAYED/REALTIME）
     */

    private Integer sendMode;
}
