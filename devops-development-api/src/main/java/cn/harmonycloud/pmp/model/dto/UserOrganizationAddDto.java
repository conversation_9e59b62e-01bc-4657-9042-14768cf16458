package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.UserRoleBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserOrganization复合对象")
public class UserOrganizationAddDto  implements Serializable {

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "用户角色集合集合")
    private List<UserRoleBase> userRoleBases;
}
