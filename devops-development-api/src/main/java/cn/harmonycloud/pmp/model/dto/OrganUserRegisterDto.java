package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@ApiModel(value = "注册用户")
public class OrganUserRegisterDto implements Serializable {

    private static final long serialVersionUID = -7743086722283617513L;

    @NotBlank(message = "用户账号不能为空")
    @ApiModelProperty(value = "账号", required = true)
    private String username;

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "角色id集合")
    private List<Long> roleIds;

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "密码",hidden = true)
    private String password;

    @ApiModelProperty(value = "状态",hidden = true)
    private Integer status;

    @ApiModelProperty(value = "扩展字段")
    private String annotations;

}
