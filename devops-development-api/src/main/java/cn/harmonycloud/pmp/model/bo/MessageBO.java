package cn.harmonycloud.pmp.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-27 7:53 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageBO implements Serializable {
    private static final long serialVersionUID = -7474897257976307082L;

    @ApiModelProperty(value = "textID")
    private Long id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "接受者ID，0表示接受者为所有人")
    private List<Long> receiveIds;
}
