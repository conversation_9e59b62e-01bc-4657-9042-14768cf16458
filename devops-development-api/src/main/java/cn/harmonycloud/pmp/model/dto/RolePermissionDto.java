package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.constant.AmpConstant;
import cn.harmonycloud.pmp.constant.DictCons.PermissionKind;
import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 角色权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class RolePermissionDto extends ResourceRole implements Serializable {


    @ApiModelProperty(value = "对象ID")
    private Long objectId;

    @ApiModelProperty(value = "类型 1.角色 2.用户 3.用户组")
    private Integer type;

    @ApiModelProperty(value = "类型 1.平台 2.租户 3.资源")
    private Integer kind;

    @ApiModelProperty(value = "权限类型 1-菜单 2-功能点 3-tab页",hidden = true)
    private List<Integer> permissionTypeList;

    @ApiModelProperty(value = "权限类型 1-菜单 2-功能点 3-tab页")
    private String permissionTypes;

    @ApiModelProperty(value = "父类菜单id")
    private Long parentId;

    @ApiModelProperty(value = "获取菜单下所有的父类id",hidden = true)
    private List<Long> parentIds;

    public void setPermissionTypes(String permissionTypes) {
        this.permissionTypes = permissionTypes;
        List<Integer> permissionTypeList= CollUtil.newArrayList();
        if(StrUtil.isNotBlank(permissionTypes)){
            String[] array= permissionTypes.split(",");
            for (int i = 0; i < array.length; i++) {
                permissionTypeList.add(Integer.parseInt(array[i]));
            }
        }
        this.setPermissionTypeList(permissionTypeList);
    }


    public Boolean isPlatOrgan() {
        return this.getOrganId().equals(AmpConstant.DEFAULT_ORGAN_ID);
    }
    public Boolean isResource() {
        return PermissionKind.RESOURCE_PERMISSION.getValue().equals(this.kind)
               || StrUtil.isNotBlank(this.getResourceTypeCode())
               || ObjectUtil.isNotNull(this.getResourceInstanceId())
                ;
    }

    @Override
    public void setResourceTypeCode(String resourceTypeCode) {
        super.setResourceTypeCode(resourceTypeCode);
        if(StrUtil.isNotBlank(resourceTypeCode)){
            this.setKind(PermissionKind.RESOURCE_PERMISSION.getValue());
        }
    }
}
