package cn.harmonycloud.pmp.model.vo;

import cn.harmonycloud.pmp.model.dto.AdministrativeDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 租户表
 * </p>
 * 用于返回
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
public class OrganizationPageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "租户id")
    private Long parentId;

    @ApiModelProperty(value = "父类租户名称")
    private String parentOrganName;

    @ApiModelProperty(value = "名称")
   private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "联系人")
    private Long contactPerson;

    @ApiModelProperty(value = "联系人")
    private String contactPersonName;

    @ApiModelProperty(value = "联系人手机号")
    private String contactPersonMobile;

    @ApiModelProperty(value = "管理员")
    private List<String> ownerNames;

    @ApiModelProperty(value = "管理员id")
    private List<Long> ownerIds;

    @ApiModelProperty(value = "状态 0启用 1停用")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "标签对象")
    private List<TagVo> tagVos;

   @ApiModelProperty(value = "标签id集合")
   private List<Long> tagIds;

    @ApiModelProperty(value = "行政组织id列表")
    private List<Long> administrativeIds;

    @ApiModelProperty(value = "行政组织对象")
    private List<AdministrativeDTO> administrativeList;

    @ApiModelProperty(value = "负责的行政组织id列表")
    private List<Long> chargeAdministrativeIds;

    @ApiModelProperty(value = "负责的行政组织列表")
    private List<AdministrativeDTO> chargeAdministrativeList;
}
