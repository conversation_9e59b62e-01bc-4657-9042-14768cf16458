package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.Role;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户组分页对象")
public class UserGroupPage extends Pagination<Role> {
    @ApiModelProperty(value = "复合查询参数,逗号隔开的查询参数")
    private String queryParam;

    @ApiModelProperty(value = "复合查询参数集合")
    private List<String> queryParams;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "用户组id")
    private Long groupId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "资源类型编码,查询资源集下面的用户时必填")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例化id,查询资源集下面的用户时必填")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "AMP资源id,子应用不要传这个参数",hidden = true)
    private Long resourceId;

    @ApiModelProperty(value = "排序，默认倒序")
    private Boolean desc=true;

    public void setQueryParam(String queryParam) {
        this.queryParam=queryParam;
        if(StrUtil.isNotBlank(queryParam)){
            List<String> queryParams= Arrays.asList(this.getQueryParam().split(","));
            this.setQueryParams(queryParams);
        }
    }
}
