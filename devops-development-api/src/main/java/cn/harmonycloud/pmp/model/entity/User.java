package cn.harmonycloud.pmp.model.entity;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user")
@ApiModel(value = "User对象", description = "用户表")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "员工编号")
    @TableField("employ_num")
    private String employNum;

    @ApiModelProperty(value = "账户名")
    @TableField("username")
    private String username;

    @ApiModelProperty(value = "密码")
    @TableField("password")
    @JsonIgnore
    private String password;

    @ApiModelProperty(value = "性别 0女 1男")
    @TableField("gender")
    private Integer gender;

    @ApiModelProperty(value = "年龄")
    @TableField("age")
    private Integer age;

    @ApiModelProperty(value = "头像")
    @TableField("avatar")
    private String avatar;


    @ApiModelProperty(value = "手机")
    @TableField("mobile")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    @TableField("email")
    private String email;

    @ApiModelProperty(value = "证件类型 0身份证 1户口簿；2护照 3军官证 4士兵证 5港澳居民来往内地通行证 6台湾同胞来往内地通行证 7临时身份证 8外国人居留证 9警官证 10其他证件")
    @TableField("ic_type")
    private Integer icType;

    @ApiModelProperty(value = "证件号码")
    @TableField("ic_number")
    private String icNumber;

    @ApiModelProperty(value = "地址")
    @TableField("address")
    private String address;

    @ApiModelProperty(value = "备注")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    @TableField("annotations")
    private String annotations;

    @ApiModelProperty(value = "状态 0.正常 1.锁定")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "是否首次登录 0-否 1-是")
    @TableField("first_login")
    private Integer firstLogin;

    @ApiModelProperty(value = "用户类型 1公司用户 2外部用户")
    @TableField(value = "user_type")
    private Integer userType;

    @ApiModelProperty(value = "密码盐")
    @TableField("salt")
    private String salt;

    @ApiModelProperty(value = "是否可编辑")
    @TableField("is_editable")
    private Boolean editable;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "乐观锁")
    @TableField("version")
    @Version
    private Integer version;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;

    @ApiModelProperty(value = "最后更新密码时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_password_time")
    private LocalDateTime updatePasswordTime;


    public static boolean equalValues(User user1, User user2) {
        return user1.getName().equals(user2.getName()) &&
                (ObjectUtil.isNull(user1.getMobile()) || user1.getMobile().equals(user2.getMobile())) &&
                (ObjectUtil.isNull(user1.getEmail()) || user1.getEmail().equals(user2.getEmail())) &&
                user1.getUsername().equals(user2.getUsername());
    }
}
