package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="TokenUser对象", description="用户表")
public class TokenUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "账户名")
    private String username;

    public TokenUser(User user){
        this.setId(user.getId());
        this.setName(user.getName());
        this.setUsername(user.getUsername());
    }
    public TokenUser(){
    }
}
