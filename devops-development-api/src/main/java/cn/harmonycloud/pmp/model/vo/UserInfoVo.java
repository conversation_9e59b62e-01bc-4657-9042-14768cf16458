package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class UserInfoVo {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "账户名")
    private String username;

    @ApiModelProperty(value = "性别 0女 1男")
    private Integer gender;

    @ApiModelProperty(value = "状态 0.正常 1.锁定")
    private Integer status;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private String annotations;


}
