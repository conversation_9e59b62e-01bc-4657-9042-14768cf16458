package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class TagsByClassificationCode implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID",hidden = true)
    private Long id;

    @ApiModelProperty(value = "标签分类编号")
    private String classificationCode;

    @ApiModelProperty(value = "作用域 1-平台及以下 2-租户及以下 3-资源及以下")
    private Integer scope;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "标签实体类型")
    private String typeCode;

    @ApiModelProperty(value = "标签实体实例id")
    private String instanceId;

    @ApiModelProperty(value = "应用code",hidden = true)
    private String appCode;

}
