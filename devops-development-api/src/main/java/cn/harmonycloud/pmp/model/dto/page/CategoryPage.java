package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.Category;
import cn.harmonycloud.pmp.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("功能模块分页对象")
public class CategoryPage extends Pagination<Category> {
    private static final long serialVersionUID = 8796628952643669932L;

    @ApiModelProperty(value = "复合查询参数")
    private String queryParam;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;


}
