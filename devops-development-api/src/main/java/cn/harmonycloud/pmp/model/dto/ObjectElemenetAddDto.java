package cn.harmonycloud.pmp.model.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @describe 角色-权限列表
 * @author: wangkuan
 * @create: 2022-01-11 17:20:14
 **/
@Data
public class ObjectElemenetAddDto implements Serializable {
    private static final long serialVersionUID = 907871241658423700L;

    @ApiModelProperty(value = "权限id")
    private Long permissionId;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "角色id")
    private Long objectId;

    @ApiModelProperty(value = "视角 1-平台 2-租户 3-资源")
    private Integer kind;

    @ApiModelProperty(value = "类型 1.角色 2.用户 3.用户组")
    private Integer type;

    @ApiModelProperty(value = "权限类型 1-菜单 2-tab页 3-功能点")
    private String permissionTypes;

    @ApiModelProperty(value = "权限类型 1-菜单 2-tab页 3-功能点",hidden = true)
    private List<Integer> permissionTypeList;

    @ApiModelProperty(value = "是否选中 0-未选中 1-选中")
    private Boolean checked;

    @ApiModelProperty(value = "应用Id")
    private Long appId;

    @ApiModelProperty(value = "资源ID",hidden = true)
    private Long resourceId=0L;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    public void setPermissionTypes(String permissionTypes) {
        this.permissionTypes = permissionTypes;
        List<Integer> permissionTypeList= CollUtil.newArrayList();
        if(StrUtil.isNotBlank(permissionTypes)){
            String[] array= permissionTypes.split(",");
            for (int i = 0; i < array.length; i++) {
                permissionTypeList.add(Integer.parseInt(array[i]));
            }
        }
        this.setPermissionTypeList(permissionTypeList);
    }
}
