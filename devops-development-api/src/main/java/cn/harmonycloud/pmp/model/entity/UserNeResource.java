package cn.harmonycloud.pmp.model.entity;

import cn.harmonycloud.pmp.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户分页对象")
public class UserNeResource extends Pagination<User> {
    private static final long serialVersionUID = 8796628952643669932L;

    @ApiModelProperty(value = "复合查询参数,逗号隔开的查询参数")
    private String queryParam;

    @ApiModelProperty(value = "跨租户协作的租户id")
    private Long organId;

    @ApiModelProperty(value = "项目的租户id")
    private Long curOrganId;

    @ApiModelProperty(value = "过滤资源类型编码")
    private String resourceTypeCode;

    @ApiModelProperty(value = "过滤资源实例化id")
    private Long resourceInstanceId;

    /**
     * 查询目标资源code， null 表示查组织成员
     * 非空查询资源成员
     */
    @ApiModelProperty(value = "资源实例化编码")
    private String iResourceTypeCode;

    /**
     * 查询目标资源code， null 表示查组织成员
     * 非空查询资源成员
     */
    @ApiModelProperty(value = "资源实例化id")
    private Long iResourceInstanceId;

    @ApiModelProperty(value = "资源id", hidden = true)
    private Long resourceId = 0L;

    @ApiModelProperty(value = "行政组织id列表")
    private List<Long> adminIds;

    @ApiModelProperty(value = "是否包含自组织")
    private Boolean adminChildren = false;
}
