package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 机构角色关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="OrganizationRole复合对象")
public class OrganizationRoleDto extends ResourceRole implements Serializable {

    @ApiModelProperty(value = "角色ID")
    private List<Long> roleIds;



}
