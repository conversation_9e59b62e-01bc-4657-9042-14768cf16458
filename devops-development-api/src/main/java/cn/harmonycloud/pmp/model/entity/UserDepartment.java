package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户部门关联表
 *
 * <AUTHOR>
 * @date 2021-12-02 10:27:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_user_department")
@ApiModel(value = "UserDepartment对象", description = "用户部门关联表")
public class UserDepartment implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(name = "用户ID")
    @TableField(value ="user_id")
    private Long userId;

    @ApiModelProperty(name = "租户ID")
    @TableField(value ="organ_id")
    private Long organId;

    @ApiModelProperty(name = "部门ID")
    @TableField(value ="dept_id")
    private Long deptId;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
