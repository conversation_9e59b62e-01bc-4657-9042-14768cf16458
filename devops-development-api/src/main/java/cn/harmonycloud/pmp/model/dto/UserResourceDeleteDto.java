package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserResourceDeleteDto {
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "资源类型,给资源集下用户分配用户时必填")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例化id,给资源集下用户分配用户时必填")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "用户角色id列表",hidden = true)
    private List<Long> roleIds;

    public ResourceRole createResourceRole(){
        ResourceRole resourceRole = new ResourceRole();
        resourceRole.setOrganId(this.organId);
        resourceRole.setResourceTypeCode(this.resourceTypeCode);
        resourceRole.setResourceInstanceId(this.resourceInstanceId);
        return resourceRole;
    }
}
