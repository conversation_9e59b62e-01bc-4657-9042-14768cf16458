package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_tag")
@ApiModel(value="Tag对象", description="标签表")
public class Tag implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "标签编号")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "作用域 1-平台及以下 2-租户及以下 3-资源及以下")
    @TableField("scope")
    @NotNull(message = "范围不能为空")
    private Integer scope;

    @ApiModelProperty(value = "标签名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "租户id")
    @TableField("organ_id")
    private Long organId;

    @ApiModelProperty(value = "标签实体类型")
    @TableField("type_code")
    private String typeCode;

    @ApiModelProperty(value = "标签实体实例id")
    @TableField("instance_id")
    private String instanceId;

    @ApiModelProperty(value = "应用code")
    @TableField("app_code")
    private String appCode;

    @ApiModelProperty(value = "标签分类id")
    @TableField("classification_id")
    private Long classificationId;

    @ApiModelProperty(value = "标签分类编号")
    @TableField(exist = false)
    private String classificationCode;

    @ApiModelProperty(value = "租户名称")
    @TableField(exist = false)
    private String organName;

    @ApiModelProperty(value = "标签分类名称")
    @TableField(exist = false)
    private String classificationName;

    @ApiModelProperty(value = "颜色")
    @TableField("colour")
    private String colour;

    @ApiModelProperty(value = "排序号")
    @TableField("sort_id")
    private Integer sortId;

    @ApiModelProperty(value = "备注")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "乐观锁")
    @TableField("version")
    @Version
    private Integer version;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;


}
