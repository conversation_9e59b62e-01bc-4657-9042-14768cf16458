package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@ApiModel(value = "用户作为资源拥有者展示对象")
public class UserOwnerVo implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "拥有者ID")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "账户名")
    private String username;

    @ApiModelProperty(value = "用户在资源下的角色列表")
    private List<RoleVo> roles;
}
