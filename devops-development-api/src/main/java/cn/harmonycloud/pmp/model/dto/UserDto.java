package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021-09-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "个人信息")
public class UserDto  implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    //message = "用户账号不能修改"
    @ApiModelProperty(value = "账号", required = false)
    private String username;

    @ApiModelProperty(value = "性别 0女 1男")
    private Integer gender;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "证件类型 0身份证 1户口簿；2护照 3军官证 4士兵证 5港澳居民来往内地通行证 6台湾同胞来往内地通行证 7临时身份证 8外国人居留证 9警官证 10其他证件")
    private Integer icType;

    @ApiModelProperty(value = "证件号码")
    private String icNumber;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "主键ID",hidden = true)
    private Long resourceId;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "类型 1平台用户 2租户用户")
    private Integer type;

    @ApiModelProperty(value = "部门id")
    private Long departId;
}
