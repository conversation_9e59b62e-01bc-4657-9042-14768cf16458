package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: CategoryAddDto
 * @projectName amp
 * @date 2020/11/4 14:58
 */

@Data
public class TagsByInstancesDto {
    @ApiModelProperty(value = "标签类型",required = true)
    String relateTypeCode;

    @ApiModelProperty(value = "实例id",required = true)
    List<Long> relateInstanceIds;
}
