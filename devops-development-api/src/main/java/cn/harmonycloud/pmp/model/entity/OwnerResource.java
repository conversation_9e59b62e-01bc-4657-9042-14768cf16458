package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 资源拥有者实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_owner_resource")
@ApiModel(value="OwnerResource对象", description="资源拥有者实例表")
public class OwnerResource implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "资源字典表的唯一编号")
    @TableField("resource_id")
    private Long resourceId;

    @ApiModelProperty(value = "子应用资源拥有者id")
    @TableField("resource_owner_id")
    private Long resourceOwnerId;

    @ApiModelProperty(value = "租户id")
    @TableField("organ_id")
    private Long organId;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
