package cn.harmonycloud.pmp.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="UserRole返回对象", description="用户角色关联返回对象")
public class UserRoleVo implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "是否是管理员 0-否 1-是")
    private Integer bolAdmin;

    @ApiModelProperty(value = "租户ID")
    private Long organId;

    @ApiModelProperty(value = "资源ID",hidden = true)
    private Long resourceId=0L;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
