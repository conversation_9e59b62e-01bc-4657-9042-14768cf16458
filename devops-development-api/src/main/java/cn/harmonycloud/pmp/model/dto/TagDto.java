package cn.harmonycloud.pmp.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="Tag复合对象")
public class TagDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "标签名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "排序号")
    private Integer sortId;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Long classificationId;

    @ApiModelProperty(value = "颜色")
    private String colour;


}
