package cn.harmonycloud.pmp.model.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @title: ServerConfig
 * @projectName src-message-svc
 * @date 2020/11/10 16:37
 */

@Data
@ToString(callSuper = true)
@TableName("hms_server_config")
public class ServerConfig{

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "租户ID")
    @TableField("tenant_id")
    private  Long tenantId;

    @ApiModelProperty(value = "服务器地址")
    @TableField("host")
    private  String host;

    @ApiModelProperty(value = "端口")
    @TableField("port")
    private int port;

    @ApiModelProperty(value = "类别 字典服务 code = sendType")
    @TableField("type")
    @NotNull(message = "类别不能为空")
    private int type;

    @ApiModelProperty(value = "应用标识")
    @TableField("app_id")
    private String appId;

    @ApiModelProperty(value = "应用密钥")
    @TableField("app_secret")
    private String appSecret;

    @ApiModelProperty(value = "用户")
    @TableField("username")
    private String username;

    @ApiModelProperty(value = "密码")
    @TableField("password")
    private String password;

    @ApiModelProperty(value = "令牌")
    @TableField("token")
    private String token;

    @ApiModelProperty(value = "发送人")
    @TableField("sender")
    private String sender;

    @ApiModelProperty(value = "调用外部api")
    @TableField("url")
    private String url;

    @ApiModelProperty(value = "请求方式 1.GET 2.POST 3.PUT 4.DELETE")
    @TableField("method")
    private Integer method;

    @ApiModelProperty(value = "参数")
    @TableField("param")
    private String param;

    @ApiModelProperty(value = "提取表达式")
    @TableField("extract_expression")
    private String extractExpression;

    @ApiModelProperty(value = "对比关系 ==等于 !=不等于 <小于 <=小于等于 >大于 >=大于等于 in左边存在于右边 nin左边不存在于右边 size数组或字符的长度 empty数组或字符为空")
    @TableField("compare")
    private String compare;

    @ApiModelProperty(value = "调用成功返回值")
    @TableField("success_value")
    private String successValue;

    @ApiModelProperty(value = "是否匿名 1-是 2-否 字典服务 code = isAnonymous")
    @TableField("is_anonymous")
    private Integer isAnonymous;

    @ApiModelProperty(value = "参数")
    @TableField(value = "param",exist = false)
    private List<Param> params;

    @ApiModelProperty(value = "钉钉关联表ID")
    @TableField(value = "ding_related_id")
    private Long dingRelatedId;

    @ApiModelProperty(value = "状态")
    @TableField(value = "status")
    private Boolean status;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    public void setParams(List<Param> params) {
        this.params = params;
        if(CollUtil.isNotEmpty(params)){
            this.setParam(JSONUtil.toJsonStr(params));
        }
    }

    public String getParam() {
        if(StrUtil.isNotBlank(param)){
            List<Param> paramMaps = JSON.parseArray(param,Param.class);
            this.setParams(paramMaps);
        }
        return param;
    }
}
