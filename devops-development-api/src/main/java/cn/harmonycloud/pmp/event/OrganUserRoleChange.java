package cn.harmonycloud.pmp.event;

import cn.harmonycloud.pmp.model.entity.RoleBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OrganUserRoleChange {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "角色id集合")
    private List<RoleBase> roleBaseList;

    @ApiModelProperty(value = "租户id")
    private Long organId;

}
