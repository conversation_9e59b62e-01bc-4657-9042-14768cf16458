package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @describe
 * @author: wang<PERSON><PERSON>
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "userWideProvider",url = "${amp.url:http://localhost:8080/}",path = FeignConstant.PROVIDER+"/userWides")
public interface IUserWideProvider {

    @GetMapping("/user")
    @ApiOperation(value = "根据用户id和类型查询外部主键", notes = "根据用户id和类型查询外部主键")
    R<List<String>> getByUserId(@RequestParam Integer type,@RequestParam List<Long> userIds);

}
