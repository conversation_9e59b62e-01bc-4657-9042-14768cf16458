package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.QueryOperationAuditListDto;
import cn.harmonycloud.pmp.model.dto.page.OperationalAuditPage;
import cn.harmonycloud.pmp.model.param.BaseOperationAuditParam;
import cn.harmonycloud.pmp.model.vo.OperationAuditSelectorAuditVo;
import cn.harmonycloud.pmp.model.vo.OperationAuditVO;
import cn.harmonycloud.pmp.resp.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @describe
 * @author: wangkuan
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "operationAuditProvider", url = "${amp.url:http://localhost:8080/}", path = FeignConstant.PROVIDER + "/operation")
public interface IOperationAuditProvider {

    @PostMapping()
    @ApiOperation(value = "新增操作审计日志", notes = "新增操作审计日志")
    R<Boolean> save(@RequestBody BaseOperationAuditParam baseOperationAuditParam);

    @GetMapping("/list")
    R<List<OperationAuditVO>> getList(@SpringQueryMap QueryOperationAuditListDto queryOperationAuditListDto);

    @GetMapping("/page")
    @ApiOperation(value = "审计日志分页", notes = "分页查询")
    R<Page<OperationAuditVO>> page(@SpringQueryMap OperationalAuditPage page);

    @GetMapping("/selectors")
    @ApiOperation("日志审计前端依赖Selector生成")
    R<OperationAuditSelectorAuditVo> getAuditSelector();

}
