package cn.harmonycloud.pmp.Enum;

import cn.harmonycloud.pmp.model.entity.AssertResult;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息发送状态
 * <AUTHOR>
 * @since 2020-11-23
 */
@Getter
@AllArgsConstructor
public enum SendStatusEnum {
    //未发
    NOT_SEND("1","未发"),
    //已发
    SUCCESS("2","已发"),
    //发送失败
    FAILED("3","发送失败");

    private String code;
    private String name;

    public static String sendResult(AssertResult assertResult){
        if(assertResult.isSuccess()){
            return SUCCESS.code;
        }else{
            return FAILED.code;
        }
    }
}
