package cn.harmonycloud.pmp.constant;

public interface AmpConstant {
    /**
     * 权限平台的token
     */
    String TOKEN_KEY = "Authorization";
    /**
     * token前缀
     */
    String AMP_TOKEN = "AMP:TOKEN:";

    /**
     * 应用前缀
     */
    String AMP_APP_PREFIX = "AMP:APP:";

    /**
     * 黑名单
     */
    String AMP_API = "AMP_API_";

    /**
     * 角色权限信息的缓存 存储形式：AMP_ROLE_PERMISSION:ORGAN_{organId}_ROLE_{roleId}
     */
    String AMP_ROLE_PERMISSION = "AMP_ROLE_PERMISSION:";

    /**
     * 用户和角色信息的缓存 存储形式：AMP_USER_ROLE:{organId}_{resourcetypeCode}_{resourceInstanceId}_USER_{userId}
     */
    String AMP_USER_ROLE = "AMP_USER_ROLE:";

    /**
     * 租户缓存前缀
     */
    String AMP_ORGAN_PREFIX = "AMP:ORGAN";

    /**
     * 租户信息缓存前缀
     */
    String AMP_ORGAN_DETAIL_PREFIX = "AMP:ORGAN:";

    /**
     * 权限平台的token
     */
    String HEADER_ORGAN_ID = "Amp-Organ-Id";

    /**
     * 权限平台的应用id
     */
    String HEADER_APP_ID = "Amp-App-Id";
    /**
     * 权限平台的应用code
     */
    String HEADER_APP_CODE = "Amp-App-Code";
    /**
     * 默认的机构id
     */
    Long DEFAULT_ORGAN_ID = 1L;
    /**
     * 初始化数据admin的用户名
     */
    String ADMIN_USERNAME = "admin";
    /**
     * 默认的AMP code
     */
    String DEFAULT_AMP_CODE ="application";

    /**
     * 默认的AMP code
     */
    String HEADER_TRACE_ID ="Trace_Id";
    /**
     * 默认的resourceId
     */
    Long ORGAN_RESOURCE_ID = 0L;

    /**
     * 上下级顶级的parentId
     */
    Long TREE_TOP_PARENT_ID = 0L;

    /**
     * 资源分组 最多展示条数
     */
    Integer MAX_RESOURCE_GROUP_SIZE = 8;

    /**
     * 资源角色前缀
     */
    String SAFETY_PASSWORD_RETRY_TIMES = "AMP:LOGIN:ERR:TIMES:";

    /**
     * 系统用户
     */
    Long SYSTEM_USER_ID = 9999L;

    /**
     * 超过3条变成树状
     */
    Integer ORGAN_LIST_TO_TREE = 3;
    /**
     * 最大访问次数
     */
    Integer MAX_BROWER_COUNT = 9999;

    /**
     * 浏览记录条数
     */
    Integer MAX_BURYING_POINT_COUNT = 50;

    /**
     * 平台顶级行政组织
     */
    String ADMINISTRATIVE_ROOT_CODE = "platform";
}

