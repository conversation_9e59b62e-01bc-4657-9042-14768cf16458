kind: ConfigMap
apiVersion: v1
metadata:
  name: devops-hzbank-svc
  namespace: cloudnative-devops
data:
  application.yml: |-
    server:
      port: 8080
      servlet:
        context-path: /hzbank
    minio:
      url: http://*************:33006
      access-key: minio
      secret-key: minio123
    spring:
      main:
        allow-bean-definition-overriding: true
      application:
        name: devops_hzbank_svc
      profiles:
        active: ${ACTIVE_ENV:dev}
      redis:
        host: *************
        port: 30037
        database: 1
        password: ZpIA0s7oCu
      datasource:
        dynamic:
          primary: master #设置默认的数据源或者数据源组,默认值即为master
          strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
          datasource:
            master:
              driver-class-name: com.mysql.cj.jdbc.Driver
              username: ${DB_USER:root}
              password: ${DB_PASSWORD:lE34lyMhtP}
              url: ************************************************************************************************************************************************************************
    #          url: ***************************************************************************************************************************
              druid:
                initial-size: 2
                validation-query: SELECT 1 FROM DUAL
    #        slave_1:
    #          driver-class-name: com.mysql.cj.jdbc.Driver
    #          username: ${DB_USER:root}
    #          password: ${DB_PASSWORD:Ab123456}
    #          url: jdbc:mysql://*************:30036/devops_scm?useUnicode=true&characterEncoding=utf-8&useSSL=false
    #          druid:
    #            initial-size: 2
    #            validation-query: SELECT 1 FROM DUAL

    cloud:
      appManage:
    #    url: http://*************:33141/
        #应用管理后端地址
        url: http://app-management-svc:9060
      scm:
        #代码仓库后端地址
        url: http://*************:33184
      pipeline:
        #流水线后端服务
        url: http://devops-pipeline-svc:8080
      system:
        #后端服务外部访问地址
        publicUrl: http://*************:33175
      product:
        # 制品库后端服务
        url: http://devops-repository-svc:8080
        # 制品库服务集群外部地址
        publicUrl:  http://*************:33188
    track-issues:
    #  url: http://*************:33131
      #跟踪事项后端服务
      url: http://track-issues-svc:8080
    biz:
      dev:
        env: harmony
      oauth:
        filters: /hzbank/subSystem/env,/hzbank/subSystem/getById/,/hzbank/scm/
    # 开启多租户配置
    hc-tenant:
      mybatis-plus:
        # 开启mybatisPlus多租户
        enable: true
      biz:
        # 租户字段 默认tenant_id
        tenant-column: tenant_id
        # 租户字段类型 默认Long 还支持String
        tenant-field-type: Long
        # 全部是租户表
        all-tenant-tables: false
        # 如果allTenantTables为false，则需要在这里配置租户表
        tenant-tables:
          - dps_system
          - dps_system_member
