package cn.harmonycloud.development.pojo.dto.devopsstage;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/7 10:44 上午
 **/
@Data
public class DevopsStageSaveRequest implements Serializable {

    private static final long serialVersionUID = 7143530597107160029L;
    @NotNull(message = "子系统id不能为空")
    private Long subSystemId;

    private List<DevopsStageConfigDto> devopsStage;
}
