package cn.harmonycloud.development.pojo.vo.feature;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/16 4:45 下午
 **/
@Data
public class FeatureClearResponse {

    @ApiModelProperty("是否成功")
    private Boolean success;

    @ApiModelProperty("未合并的分支列表")
    private List<String> nMerge;

    @ApiModelProperty("未合并的分支数量")
    private Integer nMergeNum;

    @ApiModelProperty("未合并的分支名称")
    private String nMergeName;

    public static FeatureClearResponse success() {
        FeatureClearResponse response = new FeatureClearResponse();
        response.setSuccess(true);
        return response;
    }

    public static FeatureClearResponse builder(List<String> nMerge){
        FeatureClearResponse response = new FeatureClearResponse();
        response.setSuccess(false);
        response.setNMerge(nMerge);
        response.setNMergeNum(nMerge.size());
        response.setNMergeName(String.join(",", nMerge));
        return response;
    }
}
