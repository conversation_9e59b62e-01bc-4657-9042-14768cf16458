package cn.harmonycloud.development.pojo.vo.feature;

import cn.harmonycloud.project.model.dto.UserDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 工作项信息实体
 * <AUTHOR>
 * @Date 2023/1/9 9:45 上午
 **/
@Data
public class WorkItem implements Serializable {

    private static final long serialVersionUID = 5413948005005374522L;
    @ApiModelProperty("工作项id")
    private Long id;
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("工作项编号")
    private String code;
    @ApiModelProperty("工作项名称")
    private String name;
    @ApiModelProperty("项目资源类型")
    private String resourceTypeCode;
    @ApiModelProperty("工作项类型id")
    private Long issuesClassicId;
    @ApiModelProperty("工作项类型名称")
    private String issuesClassicName;
    @ApiModelProperty("状态名称")
    private String status;
    @ApiModelProperty("迭代名称")
    private String sprint;
    @ApiModelProperty("版本名称")
    private String version;
    @ApiModelProperty("优先级")
    private String priority;
    @ApiModelProperty("负责人")
    private List<UserDTO> ownerList;
    @ApiModelProperty("负责人")
    private String assignedName;
}
