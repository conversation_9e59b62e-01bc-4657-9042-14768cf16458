package cn.harmonycloud.development.pojo.dto.feature;

import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/15 3:22 下午
 **/
@Data
public class FeatureBranchDTO implements Serializable {

    /**
     * 分支模型
     */
    private String branchModel;

    /**
     * 受控分支
     */
    private String branchName;

    /**
     * 开发分支
     */
    private String branchDevName;

    /**
     * 代码库名称
     */
    private String codeName;

    /**
     * 特性id
     */
    private Long featureId;

    /**
     * 子系统id
     */
    private Long subSystemId;

    /**
     * 创建时的来源ref
     */
    private String sourceRef;

    /**
     * 创建时commit
     */
    private String sourceCommit;

    /**
     * 清理时受控分支commit
     */
    private String clearCommit;


    /**
     * 清理时开发分支commit
     */
    private String clearDevCommit;

    /**
     * 清理时开发分支commit
     */
    private Boolean isClear;


}
