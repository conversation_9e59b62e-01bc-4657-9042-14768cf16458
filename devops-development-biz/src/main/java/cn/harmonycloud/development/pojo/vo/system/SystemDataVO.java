package cn.harmonycloud.development.pojo.vo.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 */
@ApiModel("系统详情实体")
@Data
public class SystemDataVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "系统中文全称")
    private String fullNameCn;

    @ApiModelProperty(value = "系统编码")
    private String code;

    @ApiModelProperty(value = "系统简介")
    private String descCn;

    @ApiModelProperty(value = "负责人")
    private String director;

    @ApiModelProperty(value = "项目列表")
    private List<Long> projectIds;


}
