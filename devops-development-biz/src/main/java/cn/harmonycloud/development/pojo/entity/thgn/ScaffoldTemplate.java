package cn.harmonycloud.development.pojo.entity.thgn;


import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description 脚手架模版表
 * <AUTHOR> Gong
 * @Date 2025/3/25
 **/
@TableName(value ="scaffold_template")
@Data
public class ScaffoldTemplate extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模版名称
     */
    private String name;

    /**
     * 模版描述
     */
    private String description;

    /**
     * 前端/后端
     */
    private String category;

    /**
     * GitLab中对应的项目id
     */
    private String gitlabProjectId;

    /**
     * Git仓库地址
     */
    private String gitRepoUrl;

    /**
     * 默认分支（如 main 或 master）
     */
    private String defaultBranch;

    /**
     * 模板版本（语义化版本，如 1.0.0）
     */
    private String version;

    /**
     * 技术栈
     */
    private String techStack;

    /**
     * logo地址
     */
    private String picUrl;

    /**
     * 脚手架的服务名
     */
    private String serviceName;

    /**
     * 后端脚手的包路径
     */
    private String packagePath;

}
