package cn.harmonycloud.development.pojo.dto.subsystem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/19 2:14 下午
 **/
@Data
public class SubsystemCreateRequest {

    @NotEmpty(message = "应用名称不能为空")
    private String fullNameCn;

    @NotEmpty(message = "应用编码不能为空")
    private String subCode;

    @ApiModelProperty("系统id")
    private Long systemId;

    private Integer gitlabId;

    @ApiModelProperty("应用简介")
    private String subDescCn;

    @ApiModelProperty("技术栈")
    @NotEmpty(message = "技术栈不能为空")
    private String technology;

    @ApiModelProperty("负责人")
    private Long techDirectorId;

}
