package cn.harmonycloud.development.pojo.vo.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 **/
@ApiModel("添加系统子系统成员实体")
@Data
public class CreateMemberVO {

    @ApiModelProperty("系统子系统id")
    private Long instanceId;

    @ApiModelProperty("成员角色id")
    private Long roleId;

    @ApiModelProperty("成员信息列表")
    private List<Long> userIds;

    private boolean checkCodeRepo = true;

    private boolean syncCodeRepo = true;
}
