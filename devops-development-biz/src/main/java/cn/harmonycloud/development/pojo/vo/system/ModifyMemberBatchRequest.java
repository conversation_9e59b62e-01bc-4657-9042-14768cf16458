package cn.harmonycloud.development.pojo.vo.system;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/9/8 3:03 下午
 **/
@Data
public class ModifyMemberBatchRequest {

    @NotEmpty(message = "缺少参数：instanceIds")
    private List<Long> instanceIds;
    @NotNull(message = "缺少参数：userId")
    private Long userId;
    @NotNull(message = "缺少参数：roleId")
    private Long roleId;
}
