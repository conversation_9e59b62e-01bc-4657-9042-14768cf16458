package cn.harmonycloud.development.pojo.vo.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/6/20 7:28 下午
 **/
@ApiModel("更新系统成员实体")
@Data
public class ModifyMemberVo {

    @ApiModelProperty("系统子系统id")
    private Long instanceId;

    @ApiModelProperty("成员角色id")
    private List<Long> roleIds;

    @ApiModelProperty("成员信息列表")
    private Long userId;

}
