package cn.harmonycloud.development.pojo.vo.feature;

import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/20 4:15 下午
 **/
@Data
public class FeatureListQuery {

    private Long director;
    private String featureName;
    private Integer featureStatus;
    private Long subSystemId;
    private Long projectId;
    private List<Long> ids;
    private String branchDesc;
    private String startTime;
    private String endTime;
    private List<Integer> featureStatusFilter;

}
