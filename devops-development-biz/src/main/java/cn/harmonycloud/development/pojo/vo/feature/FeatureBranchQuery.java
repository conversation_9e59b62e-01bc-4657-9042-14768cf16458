package cn.harmonycloud.development.pojo.vo.feature;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/8 3:35 下午
 **/
@Data
public class FeatureBranchQuery implements Serializable {

    private static final long serialVersionUID = -4839908235940728156L;
    private Long subSystemId;
    private String branchName; // 模糊查询
    private List<Long> featureIds;
    private List<String> branchNames;
    private List<String> branchDevNames;

    public FeatureBranchQuery(){

    }
    public FeatureBranchQuery(String branchName, List<Long> featureIds){
        this.branchName = branchName;
        this.featureIds = featureIds;
    }

    public void addBranch(String branchName){
        if(branchName == null || branchName.length() == 0){
            return ;
        }
        if(branchNames== null){
            branchNames = new ArrayList<>();
        }
        branchNames.add(branchName);
    }

    public void addBranchDev(String branchDevName){
        if(branchDevName == null || branchDevName.length() == 0){
            return ;
        }
        if(branchDevNames== null){
            branchDevNames = new ArrayList<>();
        }
        branchDevNames.add(branchDevName);
    }
}
