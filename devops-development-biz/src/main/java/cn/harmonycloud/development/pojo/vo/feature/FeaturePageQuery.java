package cn.harmonycloud.development.pojo.vo.feature;

import cn.harmonycloud.development.pojo.query.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/20 11:32 上午
 **/
@Data
public class FeaturePageQuery extends PageQuery {

    // 子系统id 必填
    private Long subSystemId;
    // 子系统id 必填
    private Long systemId;
    // 特性类型
    private Integer featureType;

    private Long projectId;

    private String featureName;

    private Integer featureStatus;

    private List<Integer> featureStatusList;

    private Long createBy;

    private Long director;

    private List<Long> ids;
    private List<Long> idsFilter;

    private String search;

    private List<Integer> bottomFeatureStatus; // 置于底部

    private List<Integer> featureStatusFilter;
}
