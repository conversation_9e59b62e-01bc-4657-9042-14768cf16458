package cn.harmonycloud.development.pojo.dto.devopsstage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description 子系统阶段列表实体
 * <AUTHOR>
 * @Date 2023/2/7 10:29 上午
 **/
@Data
@ApiModel("子系统阶段列表实体")
public class DevopsStageDto implements Serializable {

    private static final long serialVersionUID = -1383615350326910024L;
    private Long id;
    @ApiModelProperty("子系统id")
    private Long subsystemId;
    @ApiModelProperty("步骤id")
    private Long stageDictId;
    @ApiModelProperty("阶段名称")
    private String name;
    @ApiModelProperty("配置状态：0-不可配置；1-可配置")
    private Integer configStatus;
    @ApiModelProperty("阶段类型")
    private Integer type;
    @ApiModelProperty("环境类型id")
    private Integer envId;
    @ApiModelProperty("通用仓库id")
    private Long rawRepoId;
    @ApiModelProperty("容器仓库id")
    private Long containerRepoId;
    @ApiModelProperty("简介")
    private String description;
    @ApiModelProperty("阶段环境数量")
    private int stageEnvNum;

    private List<Integer> accessFeatureStatus = new ArrayList<>();

}
