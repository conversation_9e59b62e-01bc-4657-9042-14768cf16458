package cn.harmonycloud.development.pojo.dto.thgn;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/19 2:14 下午
 **/
@Data
@Accessors(chain = true)
public class ApplicationProgramDTO {

    private Long systemId;


    private Long id;
    /**
     * 所属系统ID
     */
    private Long applicationId;

    /**
     * 系统的英文名称
     */
    private String applicationEnName;


    /**
     * 应用程序编码（英文唯一标识）
     */
    private String programCode;

    /**
     * 应用程序中文名称
     */
    private String programNameCn;

    /**
     * 应用程序英文名称
     */
    private String programNameEn;

    /**
     * 当前运行版本号
     */
    private String currentRunningVersion;

    /**
     * 程序状态（如：开发中/已上线）
     */
    private String programStatus;

    /**
     * 技术栈标签,例如spingboot
     */
    private String technicalStackTags;

    /**
     * 技术栈类型 目前支持前端 后端
     */
    private String technologyStack;


    /**
     * 应用健康状态（正常/警告/异常）
     */
    private String health;

    /**
     * 开发负责人ID
     */
    private List<Long> developDirectorId;

    /**
     * 应用程序中文描述
     */
    private String programDescCn;

    /**
     * 关联的GitLab项目ID
     */
    private String gitlabId;

    /**
     * 使用的脚手架模板ID
     */
    private String scaffoldTemplateId;

    /**
     * 使用的脚手架模板ID
     */
    private String scaffoldTemplateName;

    /**
     * 是否启用GitLab集成
     */
    private Boolean gitlabEnabled;

    /**
     * 是否为新创建的程序（用于区分新建/更新操作）
     */
    private Boolean whetherNewlyBuilt;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 关联git地址
     */
    private String relateGitUrl;

    /**
     * 业务域
     */
    private String businessDomain;

    /**
     * 组件数组
     *
     */
    private List<Integer> scaffoldComponentId;
}
