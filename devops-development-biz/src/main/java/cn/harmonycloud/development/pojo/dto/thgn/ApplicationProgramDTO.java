package cn.harmonycloud.development.pojo.dto.thgn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@Accessors(chain = true)
@ApiModel("应用程序")
public class ApplicationProgramDTO {
    @ApiModelProperty("应用程序的唯一标识")
    private Long id;

    @ApiModelProperty("系统ID")
    private Long systemId;

    @ApiModelProperty(name ="所属系统ID" )
    private Long applicationId;

    @ApiModelProperty(name ="系统的英文名称" )
    private String applicationEnName;

    @ApiModelProperty("应用程序编码（英文唯一标识）")
    private String programCode;

    @ApiModelProperty("应用程序中文名称")
    private String programNameCn;

    @ApiModelProperty("应用程序英文名称")
    private String programNameEn;

    @ApiModelProperty("当前运行版本号")
    private String currentRunningVersion;

    @ApiModelProperty("程序状态（如：开发中/已上线）")
    private String programStatus;

    @ApiModelProperty("技术栈标签，例如springboot")
    private String technicalStackTags;

    @ApiModelProperty("技术栈类型（前端/后端）")
    private String technologyStack;

    @ApiModelProperty("应用健康状态（正常/警告/异常）")
    private String health;

    @ApiModelProperty("开发负责人ID列表")
    private List<Long> developDirectorId;

    @ApiModelProperty("应用程序中文描述")
    private String programDescCn;

    @ApiModelProperty("关联的GitLab项目ID")
    private String gitlabId;

    @ApiModelProperty("使用的脚手架模板ID")
    private String scaffoldTemplateId;

    @ApiModelProperty("使用的脚手架模板名称")
    private String scaffoldTemplateName;

    @ApiModelProperty("是否启用GitLab集成")
    private Boolean gitlabEnabled;

    @ApiModelProperty("是否为新创建的程序（用于区分新建/更新操作）")
    private Boolean whetherNewlyBuilt;

    @ApiModelProperty("包名")
    private String packageName;

    @ApiModelProperty("关联git地址")
    private String relateGitUrl;

    @ApiModelProperty("业务域")
    private String businessDomain;

    @ApiModelProperty("脚手架组件ID列表")
    private List<Integer> scaffoldComponentId;
}
