package cn.harmonycloud.development.pojo.dto.system;

import cn.harmonycloud.enums.BranchModelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/18 3:14 下午
 **/
@Data
@ApiModel("创建系统")
public class SystemCreateRequest implements Serializable {

    private static final long serialVersionUID = 2673030007638305332L;

    @ApiModelProperty("系统名称")
    @NotEmpty(message = "系统名称不能为空")
    private String subFullNameCn;

    @ApiModelProperty("系统编码")
    @NotEmpty(message = "系统编码不能为空")
    private String sysCode;

    // 分支模型 GENERAL-普通   CONTROLLED-受控 // GENERAL
    @ApiModelProperty("分支模型")
    private String branchModel = BranchModelEnum.GENERAL.getCode();

    @ApiModelProperty("关联项目")
    private List<Long> projectIds;

    @ApiModelProperty("负责人id")
    private Long projectDirectorId;

    @ApiModelProperty("系统简介")
    private String sysDescCn;

    @ApiModelProperty("代码库分组id")
    private Integer gitGroupId;

}
