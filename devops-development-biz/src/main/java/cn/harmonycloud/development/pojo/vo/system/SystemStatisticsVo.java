package cn.harmonycloud.development.pojo.vo.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/5 4:15 下午
 **/
@Data
@ApiModel("统计列表放回实体类")
public class SystemStatisticsVo implements Serializable {

    private static final long serialVersionUID = 4814112360536840235L;
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户姓名")
    private String name;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("特性总数")
    private Integer sumFeature;

    @ApiModelProperty("完成特性数")
    private Integer finishFeature;
}
