package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/1 5:25 下午
 **/
@TableName(value ="feature_label")
@Data
public class DevopsLabel implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 2345462254402942272L;
    @TableId(type = IdType.AUTO)
    private Long id;
    private String classificationCode;
    private Long instanceId;
    private Long labelId;
}
