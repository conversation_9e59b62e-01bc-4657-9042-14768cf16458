package cn.harmonycloud.development.pojo.vo.feature;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("跟踪事项特性创建")
public class FeatureCreateRequestDto {
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("功能分支id")
    private Long featureId;

    @ApiModelProperty("应用id")
    private Long subsystemId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("跟踪事项类型id")
    private Long issuesTypeId;

    @ApiModelProperty("需求id")
    private List<Long> TaskIds;

    @ApiModelProperty("跟踪事项类型编码")
    private String issuesTypeCode;

    @ApiModelProperty("跟踪事项类型名称")
    private String issuesTypeName;
}
