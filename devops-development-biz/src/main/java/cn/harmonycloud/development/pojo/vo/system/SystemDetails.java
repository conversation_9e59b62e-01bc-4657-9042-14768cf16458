package cn.harmonycloud.development.pojo.vo.system;

import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/18 4:45 下午
 **/
@Data
public class SystemDetails {

    @ApiModelProperty("系统id")
    private Long id;

    @ApiModelProperty("系统名称")
    private String subFullNameCn;

    @ApiModelProperty("系统编码")
    private String sysCode;

    @ApiModelProperty("分支模型")
    private String branchModel;

    @ApiModelProperty("负责人id")
    private Long projectDirectorId;

    @ApiModelProperty("负责人名称")
    private String projectDirectorName;

    @ApiModelProperty("系统简介")
    private String sysDescCn;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("标签")
    private List<Long> labels;

    @ApiModelProperty("项目id列表")
    private List<Long> projectIds;

    private GroupDto codeGroup;


}
