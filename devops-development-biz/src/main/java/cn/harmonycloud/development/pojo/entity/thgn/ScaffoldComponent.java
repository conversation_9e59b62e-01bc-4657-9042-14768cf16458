package cn.harmonycloud.development.pojo.entity.thgn;

import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "scaffold_component", autoResultMap = true)
public class ScaffoldComponent extends BaseEntity {

    /**
     * 组件唯一标识（主键）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 组件英文名
     */
    private String nameEn;

    /**
     * 组件功能描述
     */
    private String description;

    /**
     * 组件分类（如 security、data）
     */
    private String category;

    /**
     * 组件类型（maven/npm）
     */
    private String packageType;

    /**
     * 默认组件（工程必须）
     */
    private Boolean depDefault;

    /**
     * 组件依赖内容（maven、npm坐标内容）
     */
    private String depContent;

    /**
     * 配置文件内容（json对象）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, String> configContent;

    /**
     * 文档地址
     */
    private String documentation;

    /**
     * 组件图标
     */
    private String icon;

    /**
     * 背景色
     */
    private String bgColor;
}
