package cn.harmonycloud.development.pojo.vo.test;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class EnvTestVersionVO {

    private Long id;

    private String testCode;

    private String versionName;

    private String directorName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime testTime;

    private String testDirectorName;

    private String testStatus;
}
