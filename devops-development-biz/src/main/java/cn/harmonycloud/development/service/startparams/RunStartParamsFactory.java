package cn.harmonycloud.development.service.startparams;

import com.google.common.collect.Maps;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/4 4:31 下午
 **/
@Component
public class RunStartParamsFactory implements ApplicationContextAware {

    private Map<String, RunStartParamsHandler> developmentHandler = Maps.newConcurrentMap();

    private Map<String, RunStartParamsHandler> handlers = Maps.newConcurrentMap();

    private Map<String, RunStartParamsHandler> runBuildHandler = Maps.newConcurrentMap();

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        Map<String, RunStartParamsHandler> beansOfType = applicationContext.getBeansOfType(RunStartParamsHandler.class);
        for (String s : beansOfType.keySet()) {
            RunStartParamsHandler handler = beansOfType.get(s);
            RunStartParamsEnum params = handler.getParams();
            if (params.getDevelopment()){
                developmentHandler.put(params.getParamsName(), handler);
            }
            if(params.getRunBuild()){
                runBuildHandler.put(params.getParamsName(), handler);
            }
            handlers.put(params.getParamsName(), handler);
        }
    }

    public Map<String, RunStartParamsHandler> getDevelopmentHandler(){
        return developmentHandler;
    }

    public Map<String, RunStartParamsHandler> getRunBuildHandler(){
        return runBuildHandler;
    }

    public RunStartParamsHandler getHandler(String paramName) {
        return handlers.get(paramName);
    }
}
