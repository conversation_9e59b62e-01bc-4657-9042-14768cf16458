package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/7 3:35 下午
 **/
@Component
public class RawRepositoryHandler implements RunStartParamsHandler {

    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.RAW_REPOSITORY;
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        DevopsStageEnvDto devopsStageEnvDto = context.getDevopsStageEnvDto();
        if(devopsStageEnvDto.getRawRepoId() != null){
            startParameter.setValue(devopsStageEnvDto.getRawRepoId().toString());
            startParameter.setUpdateFlag(Boolean.FALSE);
        }
    }
}
