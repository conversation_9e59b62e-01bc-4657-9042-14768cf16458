package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.MergeTaskRepository;
import cn.harmonycloud.development.outbound.SystemDictRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.pojo.entity.MergeTask;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 代码合并分组id
 * <AUTHOR>
 * @Date 2023/8/4 4:38 下午
 **/
@Component
public class GroupIdHandler implements RunStartParamsHandler {

    @Autowired
    private MergeTaskRepository mergeTaskRepository;
    @Autowired
    private SystemDictRepository systemDictRepository;

    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.GROUP_ID;
    }

    @Override
    public Boolean condition(RunStartParamsContext context) {
        Map<String, JenkinsFileStartParameter> paramsMap = context.getRunStartParams().stream().collect(Collectors.toMap(JenkinsFileStartParameter::getParamName, p -> p));
        if(!paramsMap.containsKey(RunStartParamsEnum.SOURCE_BRANCHES.getParamsName())){
            return Boolean.FALSE;
        }
        return RunStartParamsHandler.super.condition(context);
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        DevopsStageEnvDto devopsStageEnvDto = context.getDevopsStageEnvDto();
        SystemDict systemDict = systemDictRepository.getCacheById(devopsStageEnvDto.getStageDictId());
        String envCode = systemDict.getDictCode() + "_" + devopsStageEnvDto.getId();
        Long mergeTaskId = mergeTaskRepository.getTaskIdByParams(devopsStageEnvDto.getSubsystemId(), envCode);
        if (mergeTaskId == null) {
            mergeTaskId = IdWorker.getId();
            MergeTask mergeTask = new MergeTask();
            mergeTask.setMergeType(SystemConstance.MergeType.MERGE_TASK);
            mergeTask.setSubSystemId(devopsStageEnvDto.getSubsystemId());
            mergeTask.setEnvCode(envCode);
            mergeTask.setMergeTaskId(mergeTaskId);
            mergeTaskRepository.save(mergeTask);
        }
        String groupId = mergeTaskId.toString();
        startParameter.setValue(groupId);
    }

    @Override
    public void setRunValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        if (!condition(context)){
            return ;
        }
        DevopsStageEnvDto devopsStageEnvDto = context.getDevopsStageEnvDto();
        SystemDict systemDict = systemDictRepository.getCacheById(devopsStageEnvDto.getStageDictId());
        String envCode = systemDict.getDictCode() + "_" + devopsStageEnvDto.getId();
        Long mergeTaskId = mergeTaskRepository.getTaskIdByParams(devopsStageEnvDto.getSubsystemId(), envCode);
        if (mergeTaskId == null) {
            mergeTaskId = IdWorker.getId();
            MergeTask mergeTask = new MergeTask();
            mergeTask.setMergeType(SystemConstance.MergeType.MERGE_TASK);
            mergeTask.setSubSystemId(devopsStageEnvDto.getSubsystemId());
            mergeTask.setEnvCode(envCode);
            mergeTask.setMergeTaskId(mergeTaskId);
            mergeTaskRepository.save(mergeTask);
        }
        String groupId = mergeTaskId.toString();
        startParameter.setValue(groupId);
    }


}
