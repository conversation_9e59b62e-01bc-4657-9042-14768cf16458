package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.system.DevopsSystemDto;
import cn.harmonycloud.development.pojo.dto.system.SystemCreateRequest;
import cn.harmonycloud.development.pojo.dto.system.SystemPageRequest;
import cn.harmonycloud.development.pojo.dto.system.SystemUpdateRequest;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;
import cn.harmonycloud.pojo.system.SystemQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【hzbank_system(系统表)】的数据库操作Service
 * @createDate 2022-07-26 16:27:56
 */
public interface SystemService {

    /**
     * 获取当前用户的系统列表
     * @return
     * @param permissionCode
     */
    List<DevopsSystem> listSystemByUser(String permissionCode);

    /**
     * 根据系统编码查询系统信息
     *
     * @param systemCode
     * @return
     */
    DevopsSystem systemCode(String systemCode);

    /**
     * 查询未被关联的代码分组
     * @return
     */
    List<GroupDto> groups();

    /**
     * 统计特性数量
     * @param systemId
     * @return
     */
    List<SystemStatisticsVo> statisticsFeature(Long systemId);

    /**
     * 系统分页列表
     *
     * @param query
     */
    Page<DevopsSystemDto> page(SystemPageRequest query);

    /**
     * 新建系统
     *
     * @param request
     * @return
     */
    DevopsSystem create(SystemCreateRequest request);

    /**
     * 系统详情
     *
     * @param id
     * @return
     */
    SystemDetails details(Long id);

    /**
     * 更新系统
     *
     * @param request
     */
    DevopsSystem modify(SystemUpdateRequest request);

    /**
     * 删除系统
     *
     * @param id
     */
    void remove(Long id);

    /**
     * 系统列表，不做权限控制
     *
     * @return
     */
    List<DevopsSystemDto> list(Long projectId);

    /**
     * 置顶请求
     *
     * @param topRequest
     */
    void top(TopRequest topRequest);

    /**
     * 根据参数查询系统，无资源权限
     *
     * @return
     * @param organId
     */
    List<DevopsSystemDto> listAll(SystemQuery organId);

    SystemDataVO getProjects(SystemDataVO systemDataVO);

    void bindProject(SystemProjectRequest systemProjectRequest);

    void unbindProject(SystemProjectRequest systemProjectRequest);

    List<SystemDataVO> getUnboundProjectSystem(List<SystemDataVO> collect);
}
