package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.CodeGroupRepository;
import cn.harmonycloud.development.outbound.db.mapper.SystemComponentMapper;
import cn.harmonycloud.development.pojo.entity.SystemComponent;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupRequest;
import cn.harmonycloud.development.service.SystemComponentService;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SystemComponentServiceImpl extends ServiceImpl<SystemComponentMapper, SystemComponent> implements SystemComponentService {

    @Autowired
    private CodeGroupRepository codeGroupRepository;

    @Override
    public void addGroupId(Long systemId, String name, String desc, String path, Integer groupId) {
        //创建组织
        Long group;
        if(groupId == null || groupId <= 0){
            GroupRequest groupRequest = new GroupRequest();
            groupRequest.setDescription(desc);
            groupRequest.setName(name);
            groupRequest.setPath(path);
            group = codeGroupRepository.createGroup(groupRequest).getId();
        }else{
            group = groupId.longValue();
        }
        //绑定系统和组织
        SystemComponent systemComponent = new SystemComponent();
        systemComponent.setSystemId(systemId);
        systemComponent.setComponent("GITLAB");
        systemComponent.setComponentKey(group.toString());
        systemComponent.setKeyType("group_id");
        this.save(systemComponent);
    }

    @Override
    public String getComponentKeyBySystemId(Long systemId) {
        LambdaQueryWrapper<SystemComponent> queryWrapper = new LambdaQueryWrapper<SystemComponent>().eq(SystemComponent::getSystemId, systemId).eq(SystemComponent::getKeyType, "group_id");
        List<SystemComponent> list = this.list(queryWrapper);
        if(list != null && !list.isEmpty()) {
            return list.get(0).getComponentKey();
        }
        return null;
    }

    @Override
    public GroupDto getGitlabGroup(Long systemId) {
        Integer codeGroupId = this.getCodeGroupId(systemId);
        return codeGroupRepository.group(codeGroupId, null);
    }

    @Override
    public Integer getCodeGroupId(Long systemId) {
        LambdaQueryWrapper<SystemComponent> queryWrapper = new LambdaQueryWrapper<SystemComponent>();
        queryWrapper.eq(SystemComponent::getComponent, "GITLAB");
        queryWrapper.eq(SystemComponent::getSystemId, systemId);
        queryWrapper.last("limit 1");
        SystemComponent systemComponent = this.getOne(queryWrapper);
        if(systemComponent == null){
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "系统数据损坏，请联系管理员");
        }
        int groupId = Integer.parseInt(systemComponent.getComponentKey());
        return groupId;
    }

    @Override
    public List<SystemComponent> getAllGitlabGroup() {
        LambdaQueryWrapper<SystemComponent> queryWrapper = new LambdaQueryWrapper<SystemComponent>();
        queryWrapper.eq(SystemComponent::getComponent, "GITLAB");
        return this.list(queryWrapper);
    }

    @Override
    public List<SystemComponent> getAllGitlabGroup(Integer groupId) {
        LambdaQueryWrapper<SystemComponent> queryWrapper = new LambdaQueryWrapper<SystemComponent>();
        queryWrapper.eq(SystemComponent::getComponent, "GITLAB");
        queryWrapper.eq(SystemComponent::getComponentKey, String.valueOf(groupId));
        queryWrapper.eq(SystemComponent::getDelFlag, 0);
        return this.list(queryWrapper);
    }

    @Override
    public void deleteComponent(Long systemId) {
        LambdaUpdateWrapper<SystemComponent> delete = new LambdaUpdateWrapper<>();
        delete.eq(SystemComponent::getSystemId, systemId);
        this.remove(delete);
    }

    @Override
    public Map<Long, SystemComponent> mapBySystemIds(List<Long> systemIds) {
        if(CollectionUtils.isEmpty(systemIds)){
            return new HashMap<>();
        }
        LambdaQueryWrapper<SystemComponent> query = new LambdaQueryWrapper<>();
        query.in(SystemComponent::getSystemId, systemIds);
        return this.list(query).stream().collect(Collectors.toMap(SystemComponent::getSystemId, c -> c));
    }

    @Override
    public void postUpdateSystem(Long id, String systemName) {
        Integer codeGroupId = getCodeGroupId(id);
        if(codeGroupId != null){
            codeGroupRepository.modifyGroupName(codeGroupId, systemName);
        }
    }
}
