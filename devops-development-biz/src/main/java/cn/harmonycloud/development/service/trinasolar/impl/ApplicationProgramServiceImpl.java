package cn.harmonycloud.development.service.trinasolar.impl;

import cn.harmonycloud.common.core.utils.string.StringUtils;
import cn.harmonycloud.development.config.GitSyncConfig;
import cn.harmonycloud.development.config.GitSyncDTO;
import cn.harmonycloud.development.convert.thgn.ApplicationProgramConvert;
import cn.harmonycloud.development.execption.thgn.AppException;
import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.outbound.api.feign.IntegrationProvider;
import cn.harmonycloud.development.outbound.db.ApplicationPersonnelRepositoryImpl;
import cn.harmonycloud.development.outbound.thgn.ApplicationProgramRepository;
import cn.harmonycloud.development.outbound.thgn.ScaffoldComponentRepository;
import cn.harmonycloud.development.outbound.thgn.ScaffoldTemplateRepository;
import cn.harmonycloud.development.outbound.util.GitlabUtil;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationCheckUserInAppDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDetailDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationUserDTO;
import cn.harmonycloud.development.pojo.entity.thgn.*;
import cn.harmonycloud.development.pojo.vo.thgn.app.ApplicationProgramVO;
import cn.harmonycloud.development.service.DevopsApplicationPipelineService;
import cn.harmonycloud.development.service.mapstruct.thgn.ApplicationProgramMapstruct;
import cn.harmonycloud.development.service.trinasolar.ApplicationProgramService;
import cn.harmonycloud.development.service.trinasolar.UserService;
import cn.harmonycloud.development.util.DateTimeUtils;
import cn.harmonycloud.development.util.RequestUtils;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.enums.thgn.ProgramEnum;
import cn.harmonycloud.issue.model.CommonResult;
import cn.harmonycloud.trinasolar.CloudProvider;
import cn.harmonycloud.trinasolar.UpmsGatewayProvider;
import cn.harmonycloud.trinasolar.UpmsProvider;
import cn.harmonycloud.trinasolar.model.*;
import cn.harmonycloud.trinasolar.model.vo.PipelineRespVO;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ApplicationProgramServiceImpl implements ApplicationProgramService {

    public static final String GITLAB = "GITLAB";
    public static final String URL = "${url}";
    public static final String GROUP = "${group}";
    public static final String ADMIN = "admin";
    public static final String DEFAULT_TIME = "1970-01-01 08:00:00";
    public static final Integer DEFAULT_SIZE = 4;
    @Autowired
    UserService userService;

    @Autowired
    private ApplicationProgramMapstruct applicationProgramMapstruct;

    @Autowired
    private ApplicationProgramRepository applicationProgramRepository;

    @Autowired
    private ScaffoldTemplateRepository scaffoldTemplateRepository;

    @Autowired
    private ScaffoldComponentRepository scaffoldComponentRepository;

    @Autowired
    private GitSyncConfig gitSyncConfig;

    @Autowired
    private ApplicationProgramConvert applicationProgramConvert;

    @Autowired
    private IntegrationProvider integrationProvider;

    // 新增Repository
    @Autowired
    private ApplicationPersonnelRepositoryImpl applicationPersonnelRepository;

    @Autowired
    private UpmsProvider upmsProvider;

    @Autowired
    private UpmsGatewayProvider upmsGatewayProvider;

    @Autowired
    private DevopsApplicationPipelineService devopsApplicationPipelineService;

    @Autowired
    private CloudProvider cloudProvider;

    @Autowired
    @Qualifier("taskExecutor")
    ThreadPoolTaskExecutor executor;


    @Override
    public void modifyApplicationProgram(ApplicationProgramDTO applicationProgramDTO) {
        // 获取参数
        String programDescCn = applicationProgramDTO.getProgramDescCn();
        List<Long> developDirectorId = applicationProgramDTO.getDevelopDirectorId();
        String currentRunningVersion = applicationProgramDTO.getCurrentRunningVersion();
        Long applicationId = applicationProgramDTO.getApplicationId();
        String relateGitUrl = applicationProgramDTO.getRelateGitUrl();
        Long id = applicationProgramDTO.getId();
        R<ProgramDTO> result = cloudProvider.getProgramById(applicationId);
        if (!result.isSuccess()) {
            throw new AppException("获取应用程序异常" + applicationId);
        }
        ProgramDTO data = result.getData();
        applicationProgramDTO.setBusinessDomain(data.getBusinessDomainValue());
        applicationProgramDTO.setApplicationEnName(data.getEnName());
        String programNameEn = applicationProgramDTO.getProgramNameEn();
        // 参数非空校验
        if (CollectionUtils.isEmpty(developDirectorId)) {
            throw new IllegalArgumentException("开发负责人列表不能为空");
        }
        if (StrUtil.isBlank(currentRunningVersion)) {
            throw new IllegalArgumentException("当前应用程序版本不能为空");
        }
        if (id == null) {
            throw new IllegalArgumentException("应用程序ID不能为空");
        }
        // 查询并校验记录存在性
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(id);
        if (applicationProgram == null) {
            throw new AppException("未找到ID为" + id + "的应用程序记录");
        }
        String existGitUrl = applicationProgram.getGitlabRepoUrl();
        // 更新实体属性
        applicationProgram.setProgramDescCn(programDescCn);
        applicationProgram.setDevelopDirectorId(developDirectorId);
        applicationProgram.setCurrentRunningVersion(currentRunningVersion);
        // 通过判断已有数据是否有初始化过
        if (StringUtils.isEmpty(existGitUrl)) {
            // 没有初始化 ，判断是否启用 true 表示不启用，修改基础信息就可以了
            if (Boolean.TRUE.equals(applicationProgramDTO.getGitlabEnabled())) {
                applicationProgramRepository.updateById(applicationProgram);
            } else {
                // 没有初始化 ，开始判断是新建还是关联
                // 新建需要自己定义url
                if (applicationProgramDTO.getWhetherNewlyBuilt()) {
                    String applicationEnName = applicationProgramDTO.getApplicationEnName();
                    GitSyncDTO gitSyncDTO = new GitSyncDTO();
                    BeanUtils.copyProperties(gitSyncConfig, gitSyncDTO);
                    relateGitUrl = getPushGitUrl(programNameEn, applicationProgramDTO.getBusinessDomain(), gitSyncDTO.getPushGitUrl(), applicationEnName);
                }
            }
            BeanUtils.copyProperties(applicationProgram, applicationProgramDTO);
            executor.execute(() -> initApp(applicationProgramDTO, applicationProgram.getProgramNameEn(), applicationId, applicationProgram));
        }
        applicationProgram.setGitlabRepoUrl(relateGitUrl);
        applicationProgramRepository.updateById(applicationProgram);
    }

    @Override
    public void createApplicationProgram(ApplicationProgramDTO applicationProgramDTO) {
        String programNameEn = applicationProgramDTO.getProgramNameEn();
        String programNameCn = applicationProgramDTO.getProgramNameCn();
        Long applicationId = applicationProgramDTO.getApplicationId();
        checkCreate(applicationId, programNameEn, programNameCn);
        ApplicationProgram applicationProgram = saveLocal(applicationProgramDTO);
        executor.execute(() -> initApp(applicationProgramDTO, programNameEn, applicationId, applicationProgram));
    }

    private void initApp(ApplicationProgramDTO applicationProgramDTO, String programNameEn, Long applicationId, ApplicationProgram applicationProgram) {
        Long programId = applicationProgram.getId();
        List<Long> userIds = applicationProgramDTO.getDevelopDirectorId();
        String scaffoldTemplateId = applicationProgramDTO.getScaffoldTemplateId();
        String applicationEnName = applicationProgramDTO.getApplicationEnName();
        // 判断是否是新建项目并且启用gitlab
        boolean isNewProject = Boolean.FALSE.equals(applicationProgramDTO.getGitlabEnabled()) && Boolean.TRUE.equals(applicationProgramDTO.getWhetherNewlyBuilt());
        log.warn("isNewProject is {}", isNewProject);
        log.warn("scaffoldTemplateId is {}", scaffoldTemplateId);
        // 模板创建、空白创建都需，使用对应模板
        if (StrUtil.isNotEmpty(scaffoldTemplateId) || isNewProject) {
            // 特殊处理空白创建
            ScaffoldTemplate scaffoldTemplate = getIfNullScaffoldTemplate(applicationProgramDTO, scaffoldTemplateId);
            String gitRepoUrl = scaffoldTemplate.getGitRepoUrl();
            String packageName = applicationProgramDTO.getPackageName();
            // 英文名称也是group名称
            GitSyncDTO gitSyncDTO = new GitSyncDTO();
            BeanUtils.copyProperties(gitSyncConfig, gitSyncDTO);
            String pushGitUrl = getPushGitUrl(programNameEn, applicationProgramDTO.getBusinessDomain(), gitSyncDTO.getPushGitUrl(), applicationEnName);
            gitSyncDTO.setPushGitUrl(pushGitUrl).setPullGitUrl(gitRepoUrl).setProgramNameEn(programNameEn).setPackageName(packageName);
            //查询starter组件
            List<ScaffoldComponent> scaffoldComponents = new ArrayList<>();
            if(applicationProgramDTO.getScaffoldComponentId() != null){
                scaffoldComponents = scaffoldComponentRepository.listByIds(applicationProgramDTO.getScaffoldComponentId());
            }
            try {
                //同步脚手架代码到master分支
                GitlabUtil.syncGitCode(gitSyncDTO, true, scaffoldTemplate,CollectionUtils.isEmpty(scaffoldComponents) ? null : scaffoldComponents);
                //创建4个环境的分支
                GitlabUtil.create4EnvBranch(gitSyncDTO.getPushGitUrl(), gitSyncConfig.getPushPrivateToken(), scaffoldTemplate.getDefaultBranch());
                //更新应用程序初始化状态
                applicationProgram.setGitlabRepoUrl(gitSyncDTO.getPushGitUrl());
                applicationProgram.setInitStatus(ProgramEnum.INIT_COMPLETE.getCode());
                applicationProgramRepository.updateById(applicationProgram);
                //创建devops的流水线
                log.warn("scaffoldTemplate is {},{},{}", scaffoldTemplate, com.alibaba.fastjson.JSONObject.toJSONString(applicationProgram), pushGitUrl);
                AppCreateReqDTO appCreateReqDTO = buildAppDTO(scaffoldTemplate, applicationProgramDTO, pushGitUrl);
                CommonResult<Boolean> result = integrationProvider.createApp(appCreateReqDTO);
                if (result != null && result.getData()) {
                    // 授权
                    addUserGrantToApplication(programId, applicationId, programNameEn, userIds, Boolean.FALSE);
                } else {
                    if (result != null) {
                        log.error("流水线创建失败{}", result.getMsg());
                    }
                    throw new GitException("流水线创建失败，详细日志请检查聚合服务！");
                }
            } catch (Exception e) {
                applicationProgram.setInitStatus(ProgramEnum.INIT_ERROR.getCode());
                applicationProgramRepository.updateById(applicationProgram);
                log.error("创建脚手架服务异常", e);
                throw new GitException("创建脚手架服务异常");
            }
        } else {
            // 正常创建不关联
            if (applicationProgramDTO.getGitlabEnabled()) {
                addUserGrantToApplication(programId, applicationId, programNameEn, userIds, Boolean.FALSE);
            } else {
                // 关联gitlab项目
                // 应用程序建完成后，创建gitlab项目
                applicationProgramDTO.setId(programId);
                String pushGitUrl = applicationProgramDTO.getRelateGitUrl();
                applicationProgram.setGitlabRepoUrl(pushGitUrl);
                addUserGrantToApplication(programId, applicationId, programNameEn, userIds, Boolean.FALSE);
                applicationProgram.setInitStatus(ProgramEnum.INIT_COMPLETE.getCode());
                applicationProgramRepository.updateById(applicationProgram);
                ScaffoldTemplate scaffoldTemplate = new ScaffoldTemplate();
                scaffoldTemplate.setTechStack(applicationProgramDTO.getTechnicalStackTags());
                scaffoldTemplate.setCategory(applicationProgramDTO.getTechnologyStack());
                AppCreateReqDTO appCreateReqDTO = buildAppDTO(scaffoldTemplate, applicationProgramDTO, pushGitUrl);
                CommonResult<Boolean> result = integrationProvider.createApp(appCreateReqDTO);
                if (result != null && result.getData()) {
                    // 授权
                    addUserGrantToApplication(programId, applicationId, programNameEn, userIds, Boolean.FALSE);
                } else {
                    if (result != null) {
                        log.error("流水线创建失败{}", result.getMsg());
                    }
                    throw new GitException("流水线创建失败，详细日志请检查聚合服务！");
                }
            }
        }
    }

    private ScaffoldTemplate getIfNullScaffoldTemplate(ApplicationProgramDTO applicationProgramDTO, String scaffoldTemplateId) {
        Boolean whetherNewlyBuilt = applicationProgramDTO.getWhetherNewlyBuilt();
        if (Boolean.TRUE.equals(whetherNewlyBuilt)) {
            scaffoldTemplateId = "0";
        }
        // 根据脚手架模版初始化代码仓库
        ScaffoldTemplate scaffoldTemplate = scaffoldTemplateRepository.getById(scaffoldTemplateId);
        if (Boolean.TRUE.equals(whetherNewlyBuilt)) {
            //空白创建，补充技术栈信息
            scaffoldTemplate.setTechStack(applicationProgramDTO.getTechnicalStackTags());
            scaffoldTemplate.setCategory(applicationProgramDTO.getTechnologyStack());
        }
        return scaffoldTemplate;
    }

    private static String getPushGitUrl(String programNameEn, String businessDomain, String pushGitUrlTmp, String applicationEnName) {
        String groupUrl = applicationEnName;
        if (StringUtils.isNotEmpty(businessDomain)) {
            groupUrl = businessDomain + "/" + applicationEnName;
        }
        String pushGitUrl = pushGitUrlTmp.replace(GROUP, groupUrl);
        pushGitUrl = pushGitUrl.replace(URL, programNameEn);
        return pushGitUrl;
    }

    private void checkCreate(Long applicationId, String programNameEn, String programNameCn) {
        CommonResult<Boolean> result = integrationProvider.initCheck(applicationId);
        if (!CommonResult.isSuccess(result.getCode()) || !result.getData()) {
            throw new AppException("应用系统未初始化，请稍等！");
        }
        // 判断一个应用系统下数据库是否有同名的
        Optional<ApplicationProgram> optionalApplicationProgram = applicationProgramRepository.lambdaQuery()
                .eq(ApplicationProgram::getApplicationId, applicationId)
                .and(q -> q.eq(ApplicationProgram::getProgramNameEn, programNameEn)
                        .or()
                        .eq(ApplicationProgram::getProgramNameCn, programNameCn))
                .eq(ApplicationProgram::getDelFlag, SystemConstance.NOT_DELETE)
                .oneOpt();
        if (optionalApplicationProgram.isPresent()) {
            throw new AppException("同一应用系统下，应用程序英文或中文名称不能重复");
        }
    }


    private static AppCreateReqDTO buildAppDTO(ScaffoldTemplate scaffoldTemplate, ApplicationProgramDTO applicationProgramDTO, String pushGitUrl) {
        AppCreateReqDTO appCreateReqDTO = new AppCreateReqDTO();
        AppTemplate appTemplate = new AppTemplate();
        appTemplate.setCategory(scaffoldTemplate.getCategory());
        appTemplate.setName(scaffoldTemplate.getName());
        appTemplate.setTechStack(scaffoldTemplate.getTechStack());
        appCreateReqDTO.setAppCode(applicationProgramDTO.getProgramNameEn()).setGitUrl(pushGitUrl).setAppTemplate(appTemplate).setProjectId(applicationProgramDTO.getApplicationId());
        return appCreateReqDTO;
    }

    @Override
    public Page<ApplicationProgramVO> listApplicationProgram(Long applicationId, String programNameCn, Page<ApplicationProgram> page, String technologyStack) {
        User currentUser = RequestUtils.getCurrentUser();
        Long userId = Long.parseLong(currentUser.getId());
        Boolean admin = userService.isAdmin(userId, applicationId);
        LambdaQueryChainWrapper<ApplicationProgram> applicationProgramLambdaQueryChainWrapper = applicationProgramRepository.lambdaQuery().like(StrUtil.isNotBlank(programNameCn), ApplicationProgram::getProgramNameCn, programNameCn)
                .eq(ApplicationProgram::getApplicationId, applicationId)
                .eq(StrUtil.isNotBlank(technologyStack), ApplicationProgram::getTechnologyStack, technologyStack)
                .orderByDesc(ApplicationProgram::getUpdateTime);
        log.info("admin is {}", admin);
        if (!admin) {
            List<DevopsApplicationProgramPersonnel> devopsApplicationProgramPersonnels = applicationPersonnelRepository.lambdaQuery().eq(DevopsApplicationProgramPersonnel::getUserId, userId).list();
            List<Long> programIds = devopsApplicationProgramPersonnels.stream().map(DevopsApplicationProgramPersonnel::getProgramId).distinct().collect(Collectors.toList());
            // 修改条件构造，避免生成id IN ()的情况
            if (CollectionUtils.isEmpty(programIds)) {
                applicationProgramLambdaQueryChainWrapper.and(wrapper -> wrapper.apply("JSON_CONTAINS(develop_director_id, '" + userId + "')").or()
                        .eq(ApplicationProgram::getCreateBy, userId));
            } else {
                applicationProgramLambdaQueryChainWrapper.and(wrapper -> wrapper.apply("JSON_CONTAINS(develop_director_id, '" + userId + "')").or()
                        .eq(ApplicationProgram::getCreateBy, userId).or().in(ApplicationProgram::getId, programIds));
            }
        }
        page = applicationProgramLambdaQueryChainWrapper.page(page);
        List<ApplicationProgram> records = page.getRecords();
        List<ApplicationProgramVO> voList = applicationProgramConvert.toVOList(records);
        Page<ApplicationProgramVO> voPage = new Page<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal()
        );
        List<Long> createByIds = records.stream().map(ApplicationProgram::getCreateBy).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(createByIds)) {
            List<UserDTO> userByIds = userService.getUserByIds(createByIds);
            Map<Long, String> userMap = userByIds.stream()
                    .collect(Collectors.toMap(
                            UserDTO::getId,
                            UserDTO::getUserRealname
                    ));
            voList.forEach(e -> {
                e.setCreatedBy(userMap.get(e.getCreateBy()));
            });
        }
        voPage.setRecords(voList);
        voPage.setPages(page.getPages());
        return voPage;
    }

    @Override
    public ApplicationCheckUserInAppDTO checkUserInApp(Long applicationId, Long userId) {
        ApplicationCheckUserInAppDTO applicationCheckUserInAppDTO = new ApplicationCheckUserInAppDTO();
        applicationCheckUserInAppDTO.setIsSuccess(Boolean.TRUE);
        List<ApplicationProgram> applicationPrograms = applicationProgramRepository.lambdaQuery().eq(ApplicationProgram::getApplicationId, applicationId).list();
        if (CollectionUtils.isEmpty(applicationPrograms)) {
            return applicationCheckUserInAppDTO;
        }
        List<Long> existDevelopersProgramIds = applicationPrograms.stream().filter(e -> {
            if (!CollectionUtils.isEmpty(e.getDevelopDirectorId()) && e.getDevelopDirectorId().contains(userId)) {
                return true;
            } else {
                return false;
            }
        }).map(ApplicationProgram::getId).collect(Collectors.toList());
        // 查询应用程序下所有创建者
        List<Long> createBy = applicationPrograms.stream().map(ApplicationProgram::getCreateBy).collect(Collectors.toList());
        // 不是开发责任人并且不是开发人员
        if (!existDevelopersProgramIds.contains(userId) && !createBy.contains(userId)) {
            return applicationCheckUserInAppDTO;
        }
        // 找出创建人在的应用程序ID
        List<Long> existCreateByProgramIds = applicationPrograms.stream().filter(e -> e.getCreateBy().equals(userId)).map(ApplicationProgram::getId).collect(Collectors.toList());
        // 开发负责人所在应用程序ID
        List<Long> existAllProgramIds = ListUtils.union(existCreateByProgramIds, existDevelopersProgramIds);
        List<ApplicationProgram> existApplicationProgram = applicationPrograms.stream().filter(e -> existAllProgramIds.contains(e.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existApplicationProgram)) {
            return applicationCheckUserInAppDTO;
        }
        StringBuilder message = new StringBuilder("删除失败，请在以下应用程序中移除该人员:[");
        String msg = existApplicationProgram.stream().map(ApplicationProgram::getProgramNameCn).collect(Collectors.joining(SystemConstance.COMMA));
        applicationCheckUserInAppDTO.setIsSuccess(Boolean.FALSE).setMessage(message.append(msg).append("]").toString());
        return applicationCheckUserInAppDTO;
    }

    @Override
    public List<String> listTechStack(ApplicationProgramDTO applicationProgramDTO) {
        Long systemId = applicationProgramDTO.getSystemId();
        List<ApplicationProgram> list =
                applicationProgramRepository.lambdaQuery().eq(Objects.nonNull(systemId), ApplicationProgram::getSystemId, systemId).list();
        return list.stream().map(ApplicationProgram::getTechnicalStackTags).distinct().collect(Collectors.toList());
    }

    @Override
    public ApplicationProgramDetailDTO applicationProgramDetail(Long programId) {
        ApplicationProgramDetailDTO applicationProgramDetailDTO = new ApplicationProgramDetailDTO();
        ApplicationProgram applicationProgram = applicationProgramRepository.lambdaQuery().eq(ApplicationProgram::getId, programId).one();
        List<Long> userIds = applicationProgram.getDevelopDirectorId();
        BeanUtils.copyProperties(applicationProgram, applicationProgramDetailDTO);
        // 回显用户
        if (!CollectionUtils.isEmpty(userIds)) {
            R<List<UserDTO>> result = upmsGatewayProvider.getByIds(userIds);
            if (!result.isSuccess()) {
                throw new AppException("用户查询异常");
            }
            List<User> entries = new ArrayList<>();
            for (UserDTO userDTO : result.getData()) {
                User user = new User();
                user.setId(String.valueOf(userDTO.getId()));
                user.setUserRealname(userDTO.getUserRealname());
                entries.add(user);
            }
            applicationProgramDetailDTO.setDevelopDirectorId(entries);
        }
        Long createBy = applicationProgram.getCreateBy();
        Long updateBy = applicationProgram.getUpdateBy();
        Map<Long, String> userMap = userService.getUserByIds(List.of(createBy, updateBy)).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getUserRealname));
        applicationProgramDetailDTO.setCreatedBy(userMap.get(createBy)).setUpdatedBy(userMap.get(updateBy));
        return applicationProgramDetailDTO;
    }

    @Override
    public Boolean addUserGrantToApplication(Long programId, Long projectId, String appName, List<Long> userIds, Boolean isDeveloper) {
        List<DevopsApplicationProgramPersonnel> devops = applicationPersonnelRepository.listByProgramId(programId);
        List<Long> grantUserIds = devops.stream().map(DevopsApplicationProgramPersonnel::getUserId).collect(Collectors.toList());
        List<Long> newUserIds = userIds.stream()
                .filter(id -> !grantUserIds.contains(id))
                .collect(Collectors.toList());
        if (newUserIds.isEmpty()) {
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "没有新用户需要添加");
        }
        // 校验用户是否存在
        R<List<UserDTO>> result = upmsGatewayProvider.getByIds(userIds);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "获取对应用户数据异常！");
        }
        List<UserDTO> users = result.getData();
        List<String> userCodes = users.stream().map(UserDTO::getUserCode).collect(Collectors.toList());
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
        String gitlabRepoUrl = applicationProgram.getGitlabRepoUrl();
        //判断是否是开发人员
        if (isDeveloper) {
            saveGrantUser(programId, users, applicationProgram);
        }
        executor.execute(() -> {
            // 判断是否有git地址，判断是否启用
            if (StringUtils.isNotBlank(gitlabRepoUrl)) {
                // 添加用户权限
                addUsersToProject(programId, users);
                // 给用户添加流水线权限
                CommonResult<Boolean> grant = integrationProvider.grant(projectId, appName, userCodes);
                if (grant.isError()) {
                    log.error("应用程序流水线添加用户错误！{}", grant.getMsg());
                    throw new AppException("应用程序流水线添加用户错误！");
                }
            }
        });
        return true;
    }


    public void addUsersToProject(Long programId, List<UserDTO> users) {
        List<String> userNames = users.stream().map(UserDTO::getUsername).filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)) {
            log.info("用户权限统一集成账户未查询到");
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "用户权限统一集成账户未查询到!");
        }
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
        if (applicationProgram == null || StringUtils.isEmpty(applicationProgram.getGitlabRepoUrl())) {
            throw new AppException("应用程序不存在或对应的git地址不存在，请检查！");
        }
        GitlabUtil.addUsersToProject(applicationProgram.getGitlabRepoUrl(), gitSyncConfig.getPushPrivateToken(), userNames);
    }

    public void removeUsersToProject(Long programId, List<UserDTO> users) {
        List<String> userNames = users.stream().map(UserDTO::getUsername).filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)) {
            log.info("用户权限统一集成账户未查询到");
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "用户权限统一集成账户未查询到!");
        }
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
        if (applicationProgram == null || StringUtils.isEmpty(applicationProgram.getGitlabRepoUrl())) {
            throw new AppException("应用程序不存在或对应的git地址不存在，请检查！");
        }
        GitlabUtil.removeUsersFromProject(applicationProgram.getGitlabRepoUrl(), gitSyncConfig.getPushPrivateToken(), userNames);
    }

    private void saveGrantUser(Long programId, List<UserDTO> users, ApplicationProgram applicationProgram) {
        for (UserDTO userDTO : users) {
            Long id = userDTO.getId();
            // 否则创建新记录
            DevopsApplicationProgramPersonnel personnel = new DevopsApplicationProgramPersonnel();
            personnel.setProgramId(programId);
            personnel.setUserId(id);
            personnel.setRoleId("developer");
            personnel.setPersonnelType("developer");
            personnel.setCreateTime(LocalDateTime.now());
            personnel.setUpdateTime(LocalDateTime.now());
            personnel.setDelFlag(SystemConstance.NOT_DELETE);
            applicationPersonnelRepository.save(personnel);
        }

    }

    @Override
    public Boolean removeUserToApplication(Long programId, Long projectId, String appName, List<Long> userIds) {
        R<List<UserDTO>> result = upmsGatewayProvider.getByIds(userIds);
        if (!result.isSuccess()) {
            throw new AppException("用户查询异常");
        }
        List<UserDTO> users = result.getData();
        List<String> userCodes = users.stream().map(UserDTO::getUserCode).collect(Collectors.toList());
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
        executor.execute(() -> {
            String scaffoldTemplateId = applicationProgram.getScaffoldTemplateId();
            if (StringUtils.isNotBlank(scaffoldTemplateId)) {
                // 给用户删除流水线权限
                integrationProvider.revoke(projectId, appName, userCodes);
                // 移除用户权限
                removeUsersToProject(programId, users);
            }
        });
        // jackjson会序列化 把部分long自动转int，这里强转
//        List<Long> exitUserIds = JSONUtil.toList(JSONUtil.toJsonStr(applicationProgram.getDevelopDirectorId()), Long.class);
//        List<Long> removeUserIds = users.stream().map(UserDTO::getId).collect(Collectors.toList());
//        List<Long> developDirectorIds = exitUserIds.stream()
//                .filter(e -> !removeUserIds.contains(e))
//                .collect(Collectors.toList());
//        applicationProgramRepository.lambdaUpdate().eq(ApplicationProgram::getId, applicationProgram.getId())
//                .set(ApplicationProgram::getDevelopDirectorId, JSONUtil.toJsonStr(developDirectorIds)).update();
        return applicationPersonnelRepository.update(
                new LambdaUpdateWrapper<DevopsApplicationProgramPersonnel>()
                        .set(DevopsApplicationProgramPersonnel::getDelFlag, SystemConstance.IS_DELETE)
                        .in(DevopsApplicationProgramPersonnel::getUserId, userIds)
                        .eq(DevopsApplicationProgramPersonnel::getProgramId, programId)
        );
    }

    @Override
    public void deleteApp(Long id) {
        LambdaQueryWrapper<ApplicationProgram> query = new LambdaQueryWrapper<>();
        query.eq(ApplicationProgram::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(ApplicationProgram::getId, id);
        ApplicationProgram program = applicationProgramRepository.getOne(query);
        if (program == null) {
            return;
        }
        // 应用程序关联表删除用户
        applicationPersonnelRepository.update(
                new LambdaUpdateWrapper<DevopsApplicationProgramPersonnel>()
                        .set(DevopsApplicationProgramPersonnel::getDelFlag, SystemConstance.IS_DELETE)
                        .eq(DevopsApplicationProgramPersonnel::getProgramId, id)
        );
        // 调用删除应用程序逻辑
        applicationProgramRepository.update(
                new LambdaUpdateWrapper<ApplicationProgram>()
                        .set(ApplicationProgram::getDelFlag, SystemConstance.IS_DELETE)
                        .eq(ApplicationProgram::getId, id)
        );
        // 删除流水线
        CommonResult<Boolean> booleanCommonResult = integrationProvider.batchDeleteByProgramId(id);
        if (!booleanCommonResult.isSuccess()) {
            log.error("流水线删除失败:{}", booleanCommonResult.getMsg());
            //throw new AppException("流水线删除失败");
        }
    }


    public ApplicationProgram saveLocal(ApplicationProgramDTO request) {
        LocalDateTime now = LocalDateTime.now();
        ApplicationProgram data = applicationProgramMapstruct.toApplicationProgram(request);
        data.setInitStatus(ProgramEnum.INIT_WAITING.getCode());
        User currentUser = RequestUtils.getCurrentUser();
        data.setCreateBy(Long.parseLong(currentUser.getId()));
        data.setCreateTime(now);
        data.setUpdateBy(Long.parseLong(currentUser.getId()));
        data.setUpdateTime(now);
        data.setDevelopDirectorId(request.getDevelopDirectorId());
        applicationProgramRepository.save(data);
        return data;
    }

    @Override
    public List<ApplicationUserDTO> listPersonnelByProgramId(Long programId) {
        List<DevopsApplicationProgramPersonnel> programPersonals = applicationPersonnelRepository.lambdaQuery()
                .eq(DevopsApplicationProgramPersonnel::getProgramId, programId)
                .eq(DevopsApplicationProgramPersonnel::getDelFlag, 0)
                .list();
        if (CollectionUtils.isEmpty(programPersonals)) {
            return null;
        }
        List<Long> userIds = programPersonals.stream().map(DevopsApplicationProgramPersonnel::getUserId).collect(Collectors.toList());
        R<List<UserDTO>> result = upmsGatewayProvider.getByIds(userIds);
        if (!result.isSuccess()) {
            throw new AppException("用户查询异常！");
        }
        List<UserDTO> users = result.getData();
        if (CollectionUtils.isEmpty(users)) {
            return null;
        }
        return applicationProgramConvert.toApplicationUserDTOs(users);
    }

    @Override
    public PageResult<PipelineRespVO> getPipelines(Long programId) {
        CommonResult<PageResult<PipelineRespVO>> result = integrationProvider.getPipelines(programId);
        if (!result.isSuccess()) {
            throw new AppException("获取流水线异常，请检查聚合聚合服务！");
        }
        PageResult<PipelineRespVO> data = result.getData();
        if (data == null || CollectionUtils.isEmpty(data.getList())) {
            log.info("流水线数据查询为空！");
            return new PageResult<>();
        }
        List<PipelineRespVO> pipelineRespList = data.getList();
        List<DevopsApplicationPipeline> list = devopsApplicationPipelineService.lambdaQuery().eq(DevopsApplicationPipeline::getProgramId, programId).list();
        // 如果库里没有，就存入库，这里主要是防止并发场景
        if (CollectionUtils.isEmpty(list)) {
            ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
            List<DevopsApplicationPipeline> applicationPipelineList = new ArrayList<>();
            pipelineRespList.forEach(e -> {
                DevopsApplicationPipeline devopsApplicationPipeline = new DevopsApplicationPipeline();
                LocalDateTime createTime = DateTimeUtils.stringToLocalDateTime(e.getCreateTime());
                devopsApplicationPipeline.setApplicationId(applicationProgram.getApplicationId()).setProgramId(programId)
                        .setPipelineId(e.getPipelineId()).setPipelineName(e.getPipelineName()).setEnvironment(e.getEnvName())
                        .setRunningStatus(e.getLatestBuildStatus()).setBuildUser(e.getBuildUser())
                        .setPipelineCreateTime(createTime);
                // 防止流水线重复入库
                if (applicationPipelineList.stream().noneMatch(p -> p.getPipelineId().equals(devopsApplicationPipeline.getPipelineId()))) {
                    applicationPipelineList.add(devopsApplicationPipeline);
                }
            });
            applicationPipelineList.forEach(pipeline -> {
                devopsApplicationPipelineService.saveOrUpdate(pipeline,
                        new LambdaUpdateWrapper<DevopsApplicationPipeline>()
                                .eq(DevopsApplicationPipeline::getPipelineId, pipeline.getPipelineId()));
            });
            pipelineRespList.forEach(p -> {
                if (DEFAULT_TIME.equals(p.getLatestBuildStartTime()) || DEFAULT_TIME.equals(p.getLatestBuildEndTime())) {
                    p.setLatestBuildEndTime(null);
                    p.setLatestBuildStartTime(null);
                }
            });
            return data;
        }
        List<String> pipelineIds = pipelineRespList.stream().map(PipelineRespVO::getPipelineId).collect(Collectors.toList());
        List<DevopsApplicationPipeline> existPipelines = devopsApplicationPipelineService.lambdaQuery().in(DevopsApplicationPipeline::getPipelineId, pipelineIds).list();
        pipelineRespList.forEach(p -> {
            existPipelines.forEach(e -> {
                if (p.getPipelineId().equals(e.getPipelineId())) {
                    // 如果是admin用户执行 就默认以库的数据为准，因为都是用admin的账号执行的
                    if (!ADMIN.equals(e.getBuildUser())) {
                        p.setBuildUser(e.getBuildUser());
                    }
                }
            });
            if (DEFAULT_TIME.equals(p.getLatestBuildStartTime()) || DEFAULT_TIME.equals(p.getLatestBuildEndTime())) {
                p.setLatestBuildEndTime(null);
                p.setLatestBuildStartTime(null);
            }
        });
        return data;
    }

    @Override
    public JSONObject deploy(Long projectId, String pipelineId) {
        String userRealName = userService.getUserRealName();
        CommonResult<JSONObject> result = integrationProvider.deploy(projectId, pipelineId);
        if (result.isError()) {
            log.error("{}", result.getMsg());
            throw new AppException("流水线执行异常：" + result.getMsg());
        }
        devopsApplicationPipelineService.lambdaUpdate().set(DevopsApplicationPipeline::getBuildUser, userRealName)
                .eq(DevopsApplicationPipeline::getPipelineId, pipelineId).update();
        return result.getData();
    }

    @Override
    public Integer count() {
        return applicationProgramRepository.count();
    }

    @Override
    public void deleteAllPrograms(Long id) {
        if (id == null) {
            return;
        }
        applicationProgramRepository.lambdaUpdate()
                .eq(ApplicationProgram::getApplicationId, id)
                .remove();
    }

    @Override
    public List<String> getGitList(Long id) {
        R<ProgramDTO> programById = cloudProvider.getProgramById(id);
        if (programById == null || R.fail().isSuccess()) {
            log.error("获取应用系统详情失败！{}", R.fail().getMessage());
            throw new AppException("获取应用系统详情失败！");
        }
        ProgramDTO data = programById.getData();
        // 业务域
        String businessDomainValue = data.getBusinessDomainValue();
        String enName = data.getEnName();
        if (StringUtils.isEmpty(businessDomainValue) || StringUtils.isEmpty(enName)) {
            log.error("未获取到id:{}应用系统！", id);
            throw new AppException("未获取到id:" + id + "应用系统");
        }
        String pushGitUrlTmp = gitSyncConfig.getPushGitUrl();
        Pattern pattern = Pattern.compile("(https://[^/]+)");
        Matcher matcher = pattern.matcher(pushGitUrlTmp);
        String gitlabDomain = matcher.find() ? matcher.group(1) : null;
        return GitlabUtil.getSubgroupProjectsGitList(businessDomainValue + "/" + enName, gitSyncConfig.getPushPrivateToken(), gitlabDomain);
    }
}
