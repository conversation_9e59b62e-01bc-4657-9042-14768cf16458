package cn.harmonycloud.development.service;

import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchStageDto;
import cn.harmonycloud.development.pojo.dto.feature.FeatureFeatureBranchDTO;
import cn.harmonycloud.development.pojo.dto.feature.FeatureTaskDTO;
import cn.harmonycloud.development.pojo.dto.feature.FeatureIssuesDto;
import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.pojo.vo.feature.*;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【hzbank_feature(特性表)】的数据库操作Service
* @createDate 2022-08-03 11:35:09
*/
public interface FeatureService {

    /**
     * 获取特性详情
     * @param id
     * @return
     */
    FeatureDetailsDto getById(Long id);

    /**
     * 获取特性详情
     * @param id
     * @return
     */
    FeatureDetailsDto getByIdWithGitlab(Long id);

    /**
     * 根据子系统查询特性列表
     * @param query
     * @return
     */
    Page<DevopsFeatureDto> pageFeature(FeaturePageRequest query);

    /**
     * 填充分支信息
     * @param listFeature
     * @param withBranchStage 是否携带分支差异
     * @return
     */
    List<FeatureFeatureBranchDTO> listFeatureBranchDto(List<DevopsFeatureDto> listFeature, boolean withBranchStage);

    /**
     * 更据子系统id查询特性列表（所有）(携带分支信息)
     * @param featureIds 特性id列表
     * @param withBranchStage 是否携带分支差异
     * @return
     */
    List<FeatureFeatureBranchDTO> listFeatureBranch(List<Long> featureIds, boolean withBranchStage);

    /**
     * 更据子系统id查询特性列表（所有）(携带分支信息)
     * @param requestDto 查询参数
     * @return
     */
    List<FeatureFeatureBranchDTO> listFeatureBranch(FeatureBranchRequestDto requestDto);

    /**
     * 更据子系统id查询特性列表（所有）(携带关联需求信息)
     * @param featureIds 特性id列表
     * @return
     */
    List<FeatureTaskDTO> listFeatureTask(List<Long> featureIds);

    /**
     * 更据子系统id查询特性列表（所有）（携带关联需求信息）
     * @param requestDto 查询参数
     * @return
     */
    List<FeatureTaskDTO> listFeatureTask(FeatureBranchRequestDto requestDto);

    /**
     * 更据子系统id查询特性列表（所有）（携带关联需求信息）
     * @param requestDto 查询参数
     * @return
     */
    Page<FeatureTaskDTO> pageFeatureTask(FeatureBranchRequestDto requestDto);

    /**
     * 工作项列表
     * @param featureId
     * @return
     */
    List<WorkItem> task(Long featureId);
    /**
     * 工作项分页列表
     * @param req
     * @return
     */
    Page<WorkItem> taskPage(FeatureTaskQueryReq req);
    /**
     * 添加特性、工作项的关联关系
     * @param req
     * @return
     */
    void addTask(FeatureTaskAddReq req);
    /**
     * 删除特性、工作项的关联关系
     * @param req
     * @return
     */
    void delTask(FeatureTaskDelReq req);
    /**
     * 查询特性编码
     * @param
     * @return
     */
    String getFeatureCode(Long featureId, Integer type);

    /**
     * 根据标签李彪查询特性id
     * @param labelId
     * @return
     */
    List<Long> listFeatureIdByLabel(List<Long> labelId);


    /**
     * 特性列表卡片形式
     * @param request
     * @return
     */
    List<DevopsFeatureDto> list(FeatureListRequest request);

    /**
     * 特性打标签
     * @param request
     */
    void featureTag(FeatureTagRequest request);

    /**
     * 更新特性状态
     * @param featureId
     * @param devSus
     */
    void updateStatus(Long featureId, Integer devSus);

    /**
     * 查询特性分支比较信息
     * @param ids
     * @param stageId
     * @return
     */
    Map<Long, BranchStageDto> mapBranchStage(List<Long> ids, Long stageId);

    /**
     *
     *
     * @param request
     */
    DevopsFeature create(FeatureCreateRequest request);

    /**
     * 更新特性信息
     *
     * @param request
     */
    void update(FeatureUpdateRequest request);

    /**
     * 逻辑删除功能分支，删除对应的物理分支
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 特性列表（携带工作项）
     *
     * @param featureIds
     * @return
     */
    List<FeatureIssuesDto> listFeatureIssues(List<Long> featureIds);

    void createFeature(FeatureCreateRequestDto request);

    /**
     * 清理功能分支
     *
     * @param request
     */
    FeatureClearResponse deleteBranch(FeatureClearRequest request);

    /**
     * 清理单个特性分支
     *
     * @param devopsFeature
     * @param featureBranch
     * @param codeRepoId
     * @param currentUser
     */
    void deleteBranch(DevopsFeature devopsFeature, FeatureBranch featureBranch, Integer codeRepoId, User currentUser);
}
