package cn.harmonycloud.development.service.trinasolar.impl;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ScaffoldComponentMapper;
import cn.harmonycloud.development.pojo.entity.thgn.ScaffoldComponent;
import cn.harmonycloud.development.service.trinasolar.ScaffoldComponentService;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ScaffoldComponentImpl extends BaseRepositoryImpl<ScaffoldComponentMapper,  ScaffoldComponent> implements ScaffoldComponentService {
}
