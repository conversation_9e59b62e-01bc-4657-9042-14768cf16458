package cn.harmonycloud.development.service;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.env.EnvVarDTO;
import cn.harmonycloud.development.pojo.entity.SubSystemEnv;
import cn.harmonycloud.development.pojo.entity.SubSystemVariable;
import cn.harmonycloud.development.pojo.vo.env.EnvVo;
import cn.harmonycloud.development.pojo.vo.env.MainVarsVO;
import cn.harmonycloud.development.pojo.vo.env.SubSystemVariableVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * @ClassName SubSystemEnvService
 * @Description
 * <AUTHOR>
 * @Date 2022/9/6 9:46 AM
 **/
public interface SubSystemEnvService extends IService<SubSystemEnv> {
    BaseResult createEnvVar(EnvVarDTO envVarDTO);

    BaseResult createVar(SubSystemVariable subSystemVariable);

    List<SubSystemVariableVO> getVars(Long envId);

    BaseResult<List<EnvVo>> getEnvs(Long subSystemId);

    BaseResult<MainVarsVO> getMainVars(Long subSystemId);

    Map getDefaultEnvVar(Long subSystemId);

    void InitSubSystemEnv(Long subSystemId);

    BaseResult updateVar(SubSystemVariable subSystemVariable);

    BaseResult getEnvNames(Long subSystemId);

    List<SubSystemVariableVO> getDefaultAndSystemVars(Long envId);

    Map<String, Object> getDefaultAndSystemVarsMap(Long envId);

    /**
     * 根据子系统code获取所有环境变量配置
     * @param subSystemCode
     * @return
     */
    Map<String, List<SubSystemVariableVO>> getAllEnvData(String subSystemCode, String envCode);
}
