package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.common.core.utils.string.StringUtils;
import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.coderepo.CodeCommit;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.outbound.api.dto.repository.DevopsRepository;
import cn.harmonycloud.development.outbound.api.dto.repository.PromotionNodeDTO;
import cn.harmonycloud.development.outbound.api.dto.repository.PromotionStrategyDTO;
import cn.harmonycloud.development.outbound.api.enums.RepositoryKindEnum;
import cn.harmonycloud.development.outbound.util.PageUtils;
import cn.harmonycloud.development.pojo.dto.feature.FeatureIssuesDto;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.repository.SystemRepoQuery;
import cn.harmonycloud.development.pojo.dto.system.DevopsSystemDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;
import cn.harmonycloud.development.pojo.entity.PromotionNodeInstance;
import cn.harmonycloud.development.pojo.vo.pipeline.ProductConfigDTO;
import cn.harmonycloud.development.pojo.vo.pipeline.VersionDeployInfo;
import cn.harmonycloud.development.pojo.vo.repository.ListInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ProductVo;
import cn.harmonycloud.development.pojo.vo.repository.PromotionNodeInstanceVo;
import cn.harmonycloud.development.pojo.vo.scm.GetConfigResponse;
import cn.harmonycloud.development.service.FeatureService;
import cn.harmonycloud.development.service.mapstruct.DevopsSystemMapstruct;
import cn.harmonycloud.development.service.mapstruct.PromotionMapstruct;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.enums.VersionStatusEnum;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.outbound.db.mapper.StrategyMapper;
import cn.harmonycloud.development.outbound.db.mapper.VersionInstanceMapper;
import cn.harmonycloud.development.outbound.db.mapper.VersionPromotionMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.*;
import cn.harmonycloud.development.pojo.entity.Strategy;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.pojo.entity.VersionManagement;
import cn.harmonycloud.development.pojo.entity.VersionPromotion;
import cn.harmonycloud.development.pojo.vo.version.*;
import cn.harmonycloud.development.service.VersionService;
import cn.harmonycloud.development.service.mapstruct.VersionMapstruct;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pojo.version.MajorVersionDto;
import cn.harmonycloud.pojo.version.VersionDto;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description:
 * @author: yx
 * @time: 2022/8/22
 */
@Service
public class VersionServiceImpl implements VersionService {

    @Resource
    StrategyMapper strategyMapper;
    @Resource
    VersionPromotionMapper versionPromotionMapper;
    @Resource
    VersionInstanceMapper versionInstanceMapper;

    @Autowired
    private VersionRepository versionRepository;

    @Autowired
    private IamRepository iamRepository;

    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private SystemDictRepository systemDictRepository;
    @Autowired
    private BuildInstanceRepository buildInstanceRepository;
    @Autowired
    private VersionMapstruct versionMapstruct;
    @Autowired
    private VersionComponentsRepository versionComponentsRepository;
    //@Autowired
    //private FeatureService featureService;
    @Autowired
    private CodeProjectRepository codeProjectRepository;
    @Autowired
    private CodeCommitRepository codeCommitRepository;
    @Autowired
    private DevopsSystemRepository systemRepository;
    @Autowired
    private DevopsSystemMapstruct systemMapstruct;
    @Autowired
    private FeatureRepository featureRepository;
    @Autowired
    private MajorVersionRepository majorVersionRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;
    @Autowired
    private SystemRepoRepository systemRepoRepository;
    @Autowired
    private DeployEnvRepository deployEnvRepository;

    @Override
    @Transactional
    public MajorVersion createVersion(CreateVersionRequest request) {
        String majorVersionNumber = request.getVersionNumberTotal();
        List<MajorVersion> majorVersions = majorVersionRepository.listByParams(request.getSubSystemId(), majorVersionNumber);
        if (CollectionUtils.isNotEmpty(majorVersions)){
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT,"此版本号已存在");
        }
        User currentUser = iamRepository.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        MajorVersion majorVersion = new MajorVersion();
        majorVersion.setCreateTime(now);
        majorVersion.setCreateBy(currentUser.getId());
        majorVersion.setUpdateBy(currentUser.getId());
        majorVersion.setUpdateTime(now);
        majorVersion.setSubSystemId(request.getSubSystemId());
        majorVersion.setVersionNumber(majorVersionNumber);
        majorVersion.setDescription(request.getDescription());
        majorVersion.setDelFlag(SystemConstance.NOT_DELETE);
        majorVersion.setVersionStatus(SystemConstance.VersionStatus.NOT_START);
        majorVersion.setSubNumber(SystemConstance.DefaultVersionNumber.DEFAULT_VERSION_NUMBER);
        majorVersion.setDirectorId(request.getDirectorId());
        majorVersionRepository.save(majorVersion);

        VersionManagement versionManagement = new VersionManagement();
        versionManagement.setCreateTime(now);
        versionManagement.setCreateBy(currentUser.getId());
        versionManagement.setUpdateBy(currentUser.getId());
        versionManagement.setUpdateTime(now);
        versionManagement.setVersionStatus(SystemConstance.VersionStatus.NOT_START);
        versionManagement.setVersionType(SystemConstance.VersionTypeContance.CUSTOM);
        versionManagement.setSubSystemId(request.getSubSystemId());
        versionManagement.setDeleteStatus(SystemConstance.NOT_DELETE);
        versionManagement.setVersionNumber(majorVersionNumber);
        versionManagement.setSubVersionNumber(SystemConstance.DefaultVersionNumber.DEFAULT_VERSION_NUMBER);
        versionManagement.setTotalVersionNumber(majorVersionNumber + "-" +SystemConstance.DefaultVersionNumber.DEFAULT_VERSION_NUMBER );
        versionManagement.setMajorVersionId(majorVersion.getId());
        versionManagement.setDirectorId(request.getDirectorId());
        versionManagement.setDescription(request.getDescription());
        versionManagement.setSwitchStatus(SystemConstance.SwitchStaus.OPENED);
        versionRepository.save(versionManagement);

        return majorVersion;
    }

    /**
     * 更新子版本号
     * @param versionNumber
     * @return
     */
    private String increase(String versionNumber) {
        int num = 1;
        if(StringUtils.isNotEmpty(versionNumber)){
            num = Integer.parseInt(versionNumber);
        }
        String formatPattern = "%02d";
        return String.format(formatPattern, ++num);
    }

    @Override
    public VersionManagement preIteration(Long subVersionId) {
        VersionManagement currentVersionManagement = versionRepository.getById(subVersionId);
        VersionManagement versionManagement = versionRepository.getOpened(currentVersionManagement.getMajorVersionId());
        String versionNumber = versionManagement.getVersionNumber();
        Long majorVersionId = versionManagement.getMajorVersionId();
        MajorVersion majorVersion = majorVersionRepository.getById(majorVersionId);
        String subVersionNumber = increase(majorVersion.getSubNumber());
        String totalVersionNumber = versionNumber + "-" +  subVersionNumber;
        versionManagement.setSubVersionNumber(subVersionNumber);
        versionManagement.setTotalVersionNumber(totalVersionNumber);
        versionManagement.setReleaseTime(null);
        return versionManagement;
    }

    /**
     * 创建上机版本 OK
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public VersionManagement versionIteration(VersionIterationRequest request) {
        //判断大版本下是否已存在该版本
        VersionManagement currentVersionManagement = versionRepository.getById(request.getSubVersionId());
        VersionManagement versionManagement = versionRepository.getOpened(currentVersionManagement.getMajorVersionId());
        MajorVersion majorVersion = majorVersionRepository.getById(versionManagement.getMajorVersionId());
        String subVersionNumber = increase(majorVersion.getSubNumber());
        if (majorVersion == null ||majorVersion.getDelFlag() == SystemConstance.IS_DELETE){
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND,"大版本不存在");
        }
        if ((request.getSubVersionNumber()!=null) && (!request.getSubVersionNumber().equals(subVersionNumber))){
            throw new SystemException(ExceptionCode.INNER_EXCEPTION,request.getSubVersionNumber() + " 版本号已存在");
        }
        Long currentUserId = request.getUserId();
        if(currentUserId == null){
            currentUserId = iamRepository.getCurrentUser().getId();
        }
        LocalDateTime now = LocalDateTime.now();
        String versionNumber = majorVersion.getVersionNumber();
        String oldSubNumber = majorVersion.getSubNumber();
        majorVersion.setSubNumber(subVersionNumber);
        majorVersion.setUpdateBy(currentUserId);
        majorVersion.setUpdateTime(now);
        if(!majorVersionRepository.optimisticUpdates(majorVersion.getId(), subVersionNumber, currentUserId, oldSubNumber)){
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "数据发生变动，请刷新后重试");
        }
        versionRepository.closeAll(versionManagement.getMajorVersionId() , currentUserId , now);
        //数据库插入版本数据
        versionManagement.setId(null);
        versionManagement.setCreateTime(now);
        versionManagement.setCreateBy(currentUserId);
        versionManagement.setUpdateBy(currentUserId);
        versionManagement.setUpdateTime(now);
        versionManagement.setVersionStatus(SystemConstance.VersionStatus.NOT_START);
        versionManagement.setDeleteStatus(SystemConstance.NOT_DELETE);
        versionManagement.setSubVersionNumber(majorVersion.getSubNumber());
        versionManagement.setTotalVersionNumber(versionNumber + "-" +majorVersion.getSubNumber() );
        versionManagement.setSwitchStatus(SystemConstance.SwitchStaus.OPENED);
        versionManagement.setReleaseTime(null);
        versionRepository.save(versionManagement);
        return versionManagement;
    }

    /**
     * 获取某个上机版本的版本信息
     *
     * @param request
     * @return
     */
    @Override
    public VersionVo selectVersion(SelectVersionVo request) {

        VersionManagement versionManagement = versionRepository.getById(request.getVersionId());
        VersionVo version = new VersionVo();
        version.setVersionNumberTotal(versionManagement.getTotalVersionNumber());
        version.setDirectorName(iamRepository.getUserById(versionManagement.getDirectorId()).getName());
        version.setCreateTime(versionManagement.getCreateTime());
        version.setId(versionManagement.getId().toString());
        version.setVersionStatus(versionManagement.getVersionStatus());
        version.setUpdateType(versionManagement.getUpdateType());
        version.setSwitchStatus(versionManagement.getSwitchStatus());
        version.setVersionType(versionManagement.getVersionType());
        version.setUpdateArea(versionManagement.getUpdateArea());
        version.setTarList(StringUtils.isNotBlank(versionManagement.getTarList()) ? Arrays.asList(versionManagement.getTarList().split(",")) : null);
        version.setYamlList(StringUtils.isNotBlank(versionManagement.getYamlList()) ? Arrays.asList(versionManagement.getYamlList().split(",")) : null);
        return version;
    }

    private String getVersion(VersionManagement versionManagement) {
        if (versionManagement.getVersionType() == null || versionManagement.getVersionType() == SystemConstance.VersionType.DATA_TIME) {
            return versionManagement.getVersionNumber() + versionManagement.getPatchNumber();
        } else {
            return versionManagement.getVersionNumber();
        }
    }


    private VersionManagement checkAndGetVersionById(Long versionId) {
        VersionManagement versionManagement = versionRepository.getById(versionId);
        if (versionManagement == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "版本不存在");
        }
        if (versionManagement.getVersionType() == null || versionManagement.getVersionType() == SystemConstance.VersionType.CUSTOMS) {
            throw new SystemException(ExceptionCode.VERSION_UPDATE_FILE, "自定义版本号不允许变更");
        }
        return versionRepository.getLastVersion(versionManagement.getSubSystemId(), versionManagement.getVersionType(), versionManagement.getVersionNumber());
    }


    @Override
    @Transactional
    public void updateVersion(UpdateVersionVo request) {
        User currentUser = iamRepository.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        UpdateMajorVersionVo updateMajorVersionVo = new UpdateMajorVersionVo();
        updateMajorVersionVo.setId(request.getId());
        updateMajorVersionVo.setDirectorId(request.getDirectorId());
        updateMajorVersionVo.setDescription(request.getDescription());
        updateMajorVersionVo.setVersionStatus(request.getVersionStatus());
        updateMajorVersion(updateMajorVersionVo);
        VersionManagement opened = versionRepository.getOpened(request.getId());
        if(opened == null){
            throw new SystemException(ExceptionCode.DATA_EXCEPTION_FAIL);
        }
        VersionManagement versionManagement = versionMapstruct.toVersionManagement(request);
        versionManagement.setId(opened.getId());
        versionManagement.setUpdateTime(now);
        versionManagement.setUpdateBy(currentUser.getId());
        if (versionManagement.getVersionStatus() == null || versionManagement.getVersionStatus() != VersionStatusEnum.DEPLOY.getCode()) {
            versionRepository.updateById(versionManagement);
            return;
        }
        VersionManagement old = versionRepository.getById(request.getId());
        if (old.getVersionStatus() == VersionStatusEnum.DEPLOY.getCode()) {
            versionRepository.updateById(versionManagement);
            return;
        }
        versionManagement.setReleaseTime(now);
        List<Long> featureIds = versionComponentsRepository.listFeatureIdByParams(opened.getId());
        versionRepository.updateById(versionManagement);
        featureRepository.releaseMark(featureIds, currentUser, now);
    }

    @Override
    public void updateMajorVersion(UpdateMajorVersionVo request) {
        User currentUser = iamRepository.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        MajorVersion majorVersion = new MajorVersion();
        majorVersion.setId(request.getId());
        majorVersion.setVersionStatus(request.getVersionStatus());
        majorVersion.setDirectorId(request.getDirectorId());
        majorVersion.setDescription(request.getDescription());
        majorVersion.setUpdateTime(now);
        majorVersion.setUpdateBy(currentUser.getId());
        majorVersionRepository.updateById(majorVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVersion(DeleteVersionVo request) {
        VersionManagement version = versionRepository.getById(request.getVersionId());
        if(version == null){
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "版本不存在或已删除");
        }
        if(version.getSwitchStatus() == 0){
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "无法删除开启的子版本");
        }
        versionRepository.deleteLogic(request.getVersionId());
        buildInstanceRepository.removeVersion(request.getVersionId());
        List<VersionManagement> versionManagementList = versionRepository.listByMajorVersionId(version.getMajorVersionId());
        if (CollectionUtils.isEmpty(versionManagementList)){
            majorVersionRepository.deleteLogic(version.getMajorVersionId());
        }
        versionComponentsRepository.removeByVersionIds(Lists.newArrayList(request.getVersionId()));
    }


    @Override
    public void promote(ProductPromotionVo request) {
        productRepository.promoteProduct(request);
    }

    @Override
    @Transactional
    public void saveVersionInstance(VersionInstance instance) {
        LambdaQueryWrapper<Strategy> query = new LambdaQueryWrapper<Strategy>()
                .eq(Strategy::getSubSystemId, instance.getSubSystemId());
        List<Strategy> strategies = strategyMapper.selectList(query);
        String strategy;
        if (CollectionUtils.isEmpty(strategies)) {
            Map<String, List<SystemDict>> allDict = systemDictRepository.getAllDict(false);
            strategy = allDict.get("DEFAULT_STRATEGY").get(0).getDictValue();
        } else {
            strategy = strategies.get(0).getEnvIdList();
        }
        versionInstanceMapper.insert(instance);
        VersionPromotion vp = new VersionPromotion();
        vp.setVersionInstanceId(instance.getId());
        vp.setPromotionStrategy(strategy);
        versionPromotionMapper.insert(vp);
    }

    @Override
    public void saveVersionInstance(Long subsystemId, Long jobId, Long buildId, Long versionId) {

        User currentUser = iamRepository.getCurrentUser();
        VersionManagement versionManagement = versionRepository.getById(versionId);
        Integer lastInstance = getLastInstance(versionId);
        VersionInstance instance = new VersionInstance();
        instance.setVersionId(versionId);
        instance.setPipelineJobId(jobId);
        instance.setPipelineBuildId(buildId);
        instance.setSubPatchNumber(lastInstance);
        instance.setSubSystemId(subsystemId);
        if (versionManagement != null) {
            instance.setSubVersionName(getVersionName(versionManagement, lastInstance));
        }
        instance.setCreateBy(currentUser.getId());
        instance.setCreateTime(LocalDateTime.now());
        this.saveVersionInstance(instance);
    }

    private String getVersionName(VersionManagement vm, Integer lastSubVersionName) {
        return vm.getVersionString() + "/" + getStringVersionNum(lastSubVersionName);
    }

    private Integer getLastInstance(Long versionId) {
        LambdaQueryWrapper<VersionInstance> query = new LambdaQueryWrapper<VersionInstance>()
                .eq(VersionInstance::getVersionId, versionId)
                .orderByDesc(VersionInstance::getSubPatchNumber);
        List<VersionInstance> versionInstances = versionInstanceMapper.selectList(query);
        if (CollectionUtils.isEmpty(versionInstances)) {
            return 1;
        }
        return versionInstances.get(0).getSubPatchNumber() + 1;
    }

    private String getStringVersionNum(Integer lastSubVersionName) {
        return lastSubVersionName < 10 ? "0" + lastSubVersionName : String.valueOf(lastSubVersionName);
    }

    @Override
    public List<MyVersionVO> myVersion(Long subSystemId) {
        List<MajorVersion> majorVersionDtoList = majorVersionRepository.listByParams(subSystemId , 10);
        return majorVersionDtoList.stream().map(v -> {
            VersionManagement opened = versionRepository.getOpened(v.getId());
            MyVersionVO myVersionVO = new MyVersionVO();
            myVersionVO.setVersionNum(v.getVersionNumber());
            if (opened != null) {
                myVersionVO.setStatus(VersionStatusEnum.getStatus(opened.getVersionStatus()));
            }else {
                myVersionVO.setStatus(VersionStatusEnum.getStatus(SystemConstance.VersionStatus.NOT_START));
            }
            return myVersionVO;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<MajorVersionDto> page(MajorVersionQuery request) {
        Page<MajorVersion> page = majorVersionRepository.pageQuery(request);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageUtils.exchangeRecordData(page, new ArrayList<>());
        }
        List<Long> userIds = page.getRecords().stream().map(MajorVersion::getDirectorId).collect(Collectors.toList());
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(userIds);
        return PageUtils.exchangeRecord(page, data -> {
            User user = userMap.getOrDefault(data.getDirectorId(), new User());
            MajorVersionDto majorVersionDto = versionMapstruct.toMajorVersionDto(data);
            majorVersionDto.setDirectorName(user.getName());
            return majorVersionDto;
        });
    }

    @Override
    public VersionDetails details(Long id) {
        VersionManagement version = versionRepository.getById(id);
        if (version == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        VersionDetails details = versionMapstruct.toVersionDetails(version);
        Set<Long> userId = Sets.newHashSet(details.getDirectorId());
        if (details.getCreateBy() != null) {
            userId.add(details.getCreateBy());
        }
        if (details.getUpdateBy() != null) {
            userId.add(details.getUpdateBy());
        }
        Map<Long, User> longUserMap = iamRepository.listUserByIdsForMap(userId.stream().collect(Collectors.toList()));
        User director = longUserMap.getOrDefault(details.getDirectorId(), new User());
        User createBy = longUserMap.getOrDefault(details.getCreateBy(), new User());
        User updateBy = longUserMap.getOrDefault(details.getUpdateBy(), new User());
        details.setDirectorName(director.getName());
        details.setCreateByName(createBy.getName());
        details.setUpdateByName(updateBy.getName());
        return details;
    }

    @Override
    public List<FeatureIssuesDto> featureQuery(Long id) {
        List<Long> featureIds = versionComponentsRepository.listFeatureIdByParams(id);
       // featureService.listFeatureIssues(featureIds);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void featureAdd(VersionFeatureModify modify) {
        if (CollectionUtils.isEmpty(modify.getFeatureIds())) {
            return;
        }
        VersionManagement version = versionRepository.getById(modify.getVersionId());
        if (version == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "版本号不存在");
        }
        User currentUser = iamRepository.getCurrentUser();
        List<VersionComponents> components = getFeatureComponents(version, modify.getFeatureIds(), currentUser);
        List<String> keys = modify.getFeatureIds().stream().map(id -> id.toString()).collect(Collectors.toList());
        versionComponentsRepository.removeByParams(modify.getVersionId(), SystemConstance.VersionComponent.FEATURE, keys);
        versionComponentsRepository.saveBatch(components);
    }

    public List<VersionComponents> getFeatureComponents(VersionManagement version, List<Long> featureIds, User currentUser){
        List<String> keys = new ArrayList<>();
        return featureIds.stream().map(fId -> {
            keys.add(fId.toString());
            VersionComponents versionComponents = new VersionComponents();
            versionComponents.setSubsystemId(version.getSubSystemId());
            versionComponents.setComponent(SystemConstance.VersionComponent.FEATURE);
            versionComponents.setComponentKey(fId.toString());
            versionComponents.setVersionId(version.getId());
            if(currentUser == null){
                versionComponents.setCreateBy(0L);
            }else{
                versionComponents.setCreateBy(currentUser.getId());
            }
            versionComponents.setCreateTime(LocalDateTime.now());
            return versionComponents;
        }).collect(Collectors.toList());
    }

    @Override
    public List<cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto> productList(Long id, String format) {
        VersionManagement version = versionRepository.getById(id);
        if (version == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "版本号不存在");
        }
        return productRepository.productList(version.getSubSystemId(), version.getVersionNumber(), format, 3);
    }

    @Override
    public VersionDeployVo deployConfig(Long id) {
        VersionManagement version = versionRepository.getById(id);
        if (version == null || version.getDeleteStatus() == SystemConstance.IS_DELETE) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "版本号不存在");
        }
        List<VersionComponents> versionComponents = versionComponentsRepository.listByParams(id, null, null);
        Map<String, List<VersionComponents>> collect = versionComponents.stream().collect(Collectors.groupingBy(VersionComponents::getComponent));
        List<VersionComponents> configRepo = collect.get(SystemConstance.VersionComponent.DEPLOY_CONFIG_REPO);
        if (CollectionUtils.isEmpty(configRepo)) {
            return new VersionDeployVo();
        }
        Long configId = Long.parseLong(configRepo.get(0).getComponentKey());
        List<EnvInfo> envInfoList = new ArrayList<>();
        VersionDeployVo versionDeployVo = new VersionDeployVo();
        List<VersionComponents> envs = collect.getOrDefault(SystemConstance.VersionComponent.DEPLOY_CONFIG_ENV, new ArrayList<>());
        for (VersionComponents env : envs) {
            EnvInfo envInfo = new EnvInfo();
            envInfo.setEnvName(env.getComponentKey());
            VersionComponents commit = versionComponentsRepository.getSubordination(id, SystemConstance.VersionComponent.DEPLOY_CONFIG_COMMIT, env.getId());
            if (commit != null) {
                CodeCommit detail = codeCommitRepository.detail(configId, commit.getComponentKey());
                envInfo.setCodeCommit(detail);
                envInfo.setCommitId(commit.getComponentKey());
            }

            envInfoList.add(envInfo);
        }
        GetConfigResponse project = codeProjectRepository.getConfig(configId);
        versionDeployVo.setConfigId(configId);
        versionDeployVo.setConfigName(project.getProjectName());
        versionDeployVo.setVersionId(id);
        versionDeployVo.setEnvInfo(envInfoList);
        return versionDeployVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDeployConfig(VersionDeployConfig config) {
        VersionManagement version = versionRepository.getById(config.getVersionId());
        if (version == null || version.getDeleteStatus() == SystemConstance.IS_DELETE) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "版本号不存在");
        }
        List<VersionComponents> versionComponents = versionComponentsRepository.listByParams(config.getVersionId(), null, null);
        User currentUser = iamRepository.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        List<VersionComponents> configRepo = versionComponents.stream()
                .filter(versionComponent -> versionComponent.getComponent().equals(SystemConstance.VersionComponent.DEPLOY_CONFIG_REPO)).collect(Collectors.toList());
        boolean isChange = false;
        Long repoId = 0l;
        Long oldRepoId = 0l;
        if (CollectionUtils.isNotEmpty(configRepo)){{
            oldRepoId = configRepo.get(0).getId();
        }}
        // 部署配置库
        if (CollectionUtils.isEmpty(configRepo)) {
            VersionComponents record = versionMapstruct.createBeanVersionComponents(version.getSubSystemId(), config.getVersionId(), currentUser.getId(), now);
            record.setComponent(SystemConstance.VersionComponent.DEPLOY_CONFIG_REPO);
            record.setComponentKey(config.getConfigId().toString());
            versionComponentsRepository.save(record);
            repoId = record.getId();
        } else if (!StringUtils.equals(configRepo.get(0).getComponentKey(), config.getConfigId().toString())) {
            VersionComponents record = configRepo.get(0);
            record.setCreateBy(currentUser.getId());
            record.setComponentKey(config.getConfigId().toString());
            record.setCreateTime(now);
            versionComponentsRepository.updateById(record);
            versionComponentsRepository.removeByParams(config.getVersionId(),SystemConstance.VersionComponent.DEPLOY_CONFIG_ENV,null);
            versionComponentsRepository.removeByParams(config.getVersionId(),SystemConstance.VersionComponent.DEPLOY_CONFIG_COMMIT,null);
            isChange = true;
        }
        versionComponents = versionComponentsRepository.listByParams(config.getVersionId(), null, null);
        List<VersionComponents> configEnv = versionComponents.stream()
                .filter(versionComponent -> versionComponent.getComponent().equals(SystemConstance.VersionComponent.DEPLOY_CONFIG_ENV)).collect(Collectors.toList());
        List<String> envComponentKey = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(configEnv)) {
             envComponentKey = configEnv.stream().map(VersionComponents::getComponentKey).collect(Collectors.toList());
        }
        List<VersionComponents> configCommit = versionComponents.stream()
                .filter(versionComponent -> versionComponent.getComponent().equals(SystemConstance.VersionComponent.DEPLOY_CONFIG_COMMIT)).collect(Collectors.toList());
        List<String> commitComponentKey = new ArrayList<>();
        Long envId = 0l;
        Long oldEnvId = 0l;
        if (envComponentKey.contains(config.getEnv())) {
            List<Long> oldEnvIds = configEnv.stream().filter(env -> env.getComponentKey().equals(config.getEnv()))
                    .map(VersionComponents::getId).collect(Collectors.toList());
            oldEnvId = oldEnvIds.get(0);
        }
        //部署环境
        if(!envComponentKey.contains(config.getEnv())) {
            VersionComponents record = versionMapstruct.createBeanVersionComponents
                    (version.getSubSystemId(), config.getVersionId(), currentUser.getId(), now);
            record.setComponent(SystemConstance.VersionComponent.DEPLOY_CONFIG_ENV);
            record.setComponentKey(config.getEnv().toString());
            record.setSubordination((!repoId.equals(0l)) ? repoId : oldRepoId);
            versionComponentsRepository.save(record);
            envId = record.getId();
            VersionComponents commit = new VersionComponents();
            commit.setVersionId(version.getId());
            commit.setSubsystemId(version.getSubSystemId());
            commit.setComponent(SystemConstance.VersionComponent.DEPLOY_CONFIG_COMMIT);
            commit.setCreateBy(currentUser.getId());
            commit.setComponentKey(config.getCommitId());
            commit.setCreateTime(now);
            commit.setSubordination(envId);
            versionComponentsRepository.save(commit);
        }else if (envComponentKey.contains(config.getEnv())) {
            if (CollectionUtils.isNotEmpty(configCommit)) {
                commitComponentKey = configCommit.stream().filter(versionComponent -> versionComponent.getComponentKey().equals(config.getEnv()))
                        .map(VersionComponents::getComponentKey).collect(Collectors.toList());
            }
            // 部署commit
            if (isChange || CollectionUtils.isEmpty(configCommit)) {
                VersionComponents record = versionMapstruct.createBeanVersionComponents(version.getSubSystemId(), config.getVersionId(), currentUser.getId(), now);
                record.setComponent(SystemConstance.VersionComponent.DEPLOY_CONFIG_COMMIT);
                record.setComponentKey(config.getCommitId());
                record.setSubordination((!envId.equals(0l)) ? envId : oldEnvId);
                versionComponentsRepository.save(record);
            } else if (!commitComponentKey.contains(config.getCommitId())) {
                VersionComponents record = new VersionComponents();
                for (VersionComponents commit : configCommit) {
                    if (commit.getSubordination().equals(oldEnvId)) {
                        record = commit;
                    }
                }
                record.setCreateBy(currentUser.getId());
                record.setComponentKey(config.getCommitId());
                record.setCreateTime(now);
                versionComponentsRepository.updateById(record);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeDeployConfig(RemoveVersionConfig versionConfig) {
        List<VersionComponents> versionComponentsList = versionComponentsRepository.getByComponentKey(versionConfig.getVersionId(), versionConfig.getComponent(), versionConfig.getKeys());
        versionComponentsRepository.removeByParams(versionConfig.getVersionId(), versionConfig.getComponent(), versionConfig.getKeys());
        List<Long> collect = versionComponentsList.stream().map(VersionComponents::getId).collect(Collectors.toList());
        if (StringUtils.equals(versionConfig.getComponent(), SystemConstance.VersionComponent.DEPLOY_CONFIG_REPO)) {
            versionComponentsRepository.removeByParams(versionConfig.getVersionId(), SystemConstance.VersionComponent.DEPLOY_CONFIG_COMMIT, null);
            versionComponentsRepository.removeByParams(versionConfig.getVersionId(), SystemConstance.VersionComponent.DEPLOY_CONFIG_ENV, null);
        }
        if (StringUtils.equals(versionConfig.getComponent(),SystemConstance.VersionComponent.DEPLOY_CONFIG_ENV)){
            versionComponentsRepository.removeBySubordination(versionConfig.getVersionId(), SystemConstance.VersionComponent.DEPLOY_CONFIG_COMMIT, collect);
        }
    }

    @Override
    public VersionDetails last(Long subsystemId) {
        VersionManagement last = versionRepository.last(subsystemId);
        if (last == null) {
            return new VersionDetails();
        }
        return details(last.getId());
    }

    @Override
    public List<MajorVersionDto> list(VersionGeneralListRequest request) {
        List<MajorVersion> majorVersions = majorVersionRepository.listByParams(request.getIds(), request.getSubsystemIds());
        return majorVersions.stream().map(version -> versionMapstruct.toMajorVersionDto(version)).collect(Collectors.toList());
    }

    @Override
    public List<DevopsSystemDto> systemVersionList(VersionSubsystemQuery versionSubsystemQuery) {
        if (CollectionUtils.isEmpty(versionSubsystemQuery.getIds())) {
            return new ArrayList<>();
        }
        List<VersionSubsystem> versionSubsystems = majorVersionRepository.versionSubsystemList(versionSubsystemQuery);
        if (CollectionUtils.isEmpty(versionSubsystems)) {
            return new ArrayList<>();
        }
        Map<Long, List<VersionSubsystem>> versionGroup = versionSubsystems.stream().collect(Collectors.groupingBy(VersionSubsystem::getSystemId));
        List<Long> systemIds = versionGroup.keySet().stream().collect(Collectors.toList());
        List<DevopsSystem> systemList = systemRepository.listByParams(systemIds);
        Set<Long> directorIds = new HashSet<>();
        for (DevopsSystem devopsSystem : systemList) {
            List<VersionSubsystem> list = versionGroup.get(devopsSystem.getId());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            Set<Long> collect = list.stream().filter(vs -> vs.getTechDirectorId() != null).map(vs -> vs.getTechDirectorId()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(collect)) {
                directorIds.addAll(collect);
            }
        }
        List<Long> majorIds = versionSubsystems.stream().map(v -> v.getVersionId()).collect(Collectors.toList());
        Map<Long, VersionManagement> versionManagements = versionRepository.mapOpened(majorIds);
        List<VersionManagement> versions = versionManagements.entrySet().stream().map(entry -> entry.getValue()).collect(Collectors.toList());
        Map<Long, List<VersionManagement>> versionGroupByMajor = versions.stream().collect(Collectors.groupingBy(VersionManagement::getMajorVersionId));

        List<VersionComponents> versionComponents = versionComponentsRepository.listByParams(versions.stream().map(v -> v.getId()).collect(Collectors.toList()), SystemConstance.VersionComponent.PRODUCT, null);
        List<Long> productIds = versionComponents.stream().map(vc -> Long.parseLong(vc.getComponentKey())).collect(Collectors.toList());
        Map<Long, List<VersionComponents>> versionComponentsGroup = versionComponents.stream().collect(Collectors.groupingBy(VersionComponents::getVersionId));
        Map<Long, DevopsProductMetadataDto> productMap = productRepository.listByIds(productIds).stream().collect(Collectors.toMap(DevopsProductMetadataDto::getId, d -> d));

        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(directorIds.stream().collect(Collectors.toList()));
        return systemList.stream().map(s -> {
            DevopsSystemDto devopsSystemDto = systemMapstruct.toDevopsSystemDto(s);
            List<VersionSubsystem> list = versionGroup.get(s.getId());
            if (list == null) {
                list = new ArrayList<>();
            }
            List<VersionSubsystemDto> collect = list.stream().map(vs -> {
                VersionSubsystemDto versionSubsystemDto = versionMapstruct.toVersionSubsystemDto(vs);
                if (vs.getTechDirectorId() != null && userMap.containsKey(vs.getTechDirectorId())) {
                    User user = userMap.get(vs.getTechDirectorId());
                    versionSubsystemDto.setTechDirectorName(user.getName());
                }
                List<VersionManagement> versionList = versionGroupByMajor.get(vs.getVersionId());
                if(CollectionUtils.isNotEmpty(versionList)){
                    List<VersionComponents> versionProducts = versionComponentsGroup.getOrDefault(versionList.get(0).getId(), new ArrayList<>());
                    List<DevopsProductMetadataDto> products = versionProducts.stream().filter(c -> productMap.containsKey(Long.parseLong(c.getComponentKey()))).map(vc -> productMap.get(Long.parseLong(vc.getComponentKey()))).collect(Collectors.toList());
                    versionSubsystemDto.setProducts(products);
                }
                return versionSubsystemDto;
            }).collect(Collectors.toList());

            devopsSystemDto.setVersionSubsystemList(collect);
            return devopsSystemDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<VersionDetailsVo> getVersionAll(Long subSystemId) {
        List<MajorVersion> majorVersions = majorVersionRepository.listByParams(subSystemId, "");
        return majorVersions.stream().map(version -> {
            VersionDetailsVo vo = new VersionDetailsVo();
            vo.setId(version.getId());
            vo.setVersionNum(version.getVersionNumber());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<VersionDto> versionList(Long majorVersionId) {
        List<VersionManagement> versionManagementList = versionRepository.listByMajorVersionId(majorVersionId);
        List<Long> userIds = new ArrayList<>();
        for (VersionManagement versionManagement : versionManagementList) {
            userIds.add(versionManagement.getDirectorId());
            userIds.add(versionManagement.getUpdateBy());
            userIds.add(versionManagement.getCreateBy());
        }
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(userIds);
        List<VersionDto> versionDtoList = new ArrayList<>();
        for (VersionManagement versionManagement : versionManagementList) {
            VersionDto versionDto = versionMapstruct.toVersionDto(versionManagement);
            versionDto.setDirectorName(userMap.getOrDefault(versionDto.getDirectorId(),new User()).getName());
            versionDto.setCreateByName(userMap.getOrDefault(versionDto.getCreateBy(),new User()).getName());
            versionDto.setUpdateByName(userMap.getOrDefault(versionDto.getUpdateBy(),new User()).getName());
            versionDtoList.add(versionDto);
        }
        return versionDtoList;
    }

    @Override
    @Transactional
    public void changeVersionStatus(ChangeVersionSwitchStatusRequest request) {
        VersionManagement versionManagement = new VersionManagement();
        User currentUser = iamRepository.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        VersionManagement byId = versionRepository.getById(request.getVersionId());
        Integer switchStatus = byId.getSwitchStatus();
        if(switchStatus.equals(request.getSwitchStatus())){
            return;
        }
        if (request.getSwitchStatus() == SystemConstance.SwitchStaus.OPENED)
        {
            versionRepository.closeAll(byId.getMajorVersionId(),currentUser.getId(),now);
        }
        versionManagement.setSwitchStatus(request.getSwitchStatus());
        versionManagement.setId(request.getVersionId());
        versionManagement.setUpdateTime(now);
        versionRepository.updateById(versionManagement);
    }

    @Override
    public List<MajorVersionDto> majorVersionList(Long subSystemId) {
        List<MajorVersion> majorVersionList =majorVersionRepository.listBySubSystemId(subSystemId);
        List<MajorVersionDto> majorVersionDtoList = majorVersionList.stream()
                .map(majorVersion -> versionMapstruct.toMajorVersionDto(majorVersion)).collect(Collectors.toList());
        return majorVersionDtoList;
    }

    @Override
    public List<String> envList(Long configId , Long versionId) {
        List<VersionComponents> versionComponents = versionComponentsRepository.listByParams(versionId, SystemConstance.VersionComponent.DEPLOY_CONFIG_ENV, null);
        List<String> collect = versionComponents.stream().map(VersionComponents::getComponentKey).collect(Collectors.toList());
        List<String> envNameList = codeProjectRepository.envList(configId);
        List<String> envList = envNameList.stream().filter(envName -> !collect.stream().anyMatch(remove -> remove.equals(envName))).collect(Collectors.toList());
        return envList;
    }


    @Override
    public List<ProductVo> listProduct(Long versionId) {
        List<VersionComponents> versionComponents = versionComponentsRepository.listByParams(versionId, SystemConstance.VersionComponent.PRODUCT, null);
        VersionManagement versionManagement = versionRepository.getById(versionId);
        DevopsSubSystem subSystem = subsystemRepository.getById(versionManagement.getSubSystemId());
        Long systemId = subSystem.getSystemId();
        List<Long> productIdList = versionComponents.stream().map(component ->Long.parseLong(component.getComponentKey())).collect(Collectors.toList());
        List<DevopsProductMetadataDto> devopsProductMetadataDtoList= productRepository.listByIds(productIdList);
        ListInstanceReq listInstanceReq = new ListInstanceReq();
        listInstanceReq.setProductIdList(productIdList);
        listInstanceReq.setSystemId(systemId);
        List<PromotionInstanceDto> promotionInstanceDtoList = productRepository.listPromotionInstance(listInstanceReq);
        Map<Long, List<PromotionNodeInstance>> collect = promotionInstanceDtoList.stream().collect(Collectors.toMap(PromotionInstanceDto::getProductId,PromotionInstanceDto::getPromotionNodeInstanceList));
        Set<Long> productIds = collect.keySet();
        List<ProductVo> productVoList = new ArrayList<>();
        for (DevopsProductMetadataDto devopsProductMetadataDto : devopsProductMetadataDtoList) {
            ProductVo productVo = new ProductVo();
            if (productIds.contains(devopsProductMetadataDto.getId())) {
                List<PromotionNodeInstance> promotionNodeInstances = collect.get(devopsProductMetadataDto.getId());
                productVo.setPromotionNodeInstanceVoList(promotionNodeInstances);
            }
            productVo.setRepoId(devopsProductMetadataDto.getRepoId());
            productVo.setFormat(devopsProductMetadataDto.getFormat());
            productVo.setProductVersion(devopsProductMetadataDto.getProductVersion());
            productVo.setProductName(devopsProductMetadataDto.getName());
            productVo.setProductId(devopsProductMetadataDto.getId());
            productVo.setRepoName(devopsProductMetadataDto.getRepoName());
            productVoList.add(productVo);
        }
        return productVoList;
    }

    @Override
    public void deleteMajorVersion(DeleteMajorVersionReq request) {
        majorVersionRepository.deleteLogic(request.getMajorVersionId());
        List<VersionManagement> versionManagements = versionRepository.listByMajorVersionId(request.getMajorVersionId());
        versionRepository.deleteLogicByMajorId(request.getMajorVersionId());
        if(CollectionUtils.isNotEmpty(versionManagements)){
            List<Long> subVersionIds = versionManagements.stream().map(v -> v.getId()).collect(Collectors.toList());
            versionComponentsRepository.removeByVersionIds(subVersionIds);
        }
    }

    @Override
    public UploadVersionProductVo getUploadProductInfo(Long versionId) {
        VersionManagement version = versionRepository.getById(versionId);
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(version.getSubSystemId());
        if(devopsSubsystem == null){
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "应用信息错误");
        }
        UploadVersionProductVo result = new UploadVersionProductVo();
        List<PromotionStrategyDTO> promotionStrategyDTOS = systemRepoRepository.listPromotionStrategy(devopsSubsystem.getSystemId(), null);
        SystemRepoQuery query = new SystemRepoQuery();
        //query.setWithProdRepository(true);
        query.setSystemId(devopsSubsystem.getSystemId());
        query.setKind(RepositoryKindEnum.ARTIFACT.getType());
        List<DevopsRepository> devopsRepositories = systemRepoRepository.listByParams(query);
        result.setDockerRepository(selectUploadRepository(promotionStrategyDTOS, devopsRepositories, "docker"));
        result.setRawRepository(selectUploadRepository(promotionStrategyDTOS, devopsRepositories, "raw"));
        result.setSubCode(devopsSubsystem.getSubCode());
        result.setTotalVersion(version.getTotalVersionNumber());
        result.setMajorVersion(version.getVersionNumber());
        result.setDockerProductName(devopsSubsystem.getSubCode().toLowerCase(Locale.ROOT) + ":" + version.getTotalVersionNumber());
        result.setRawProductName(devopsSubsystem.getSubCode() + "_" + version.getTotalVersionNumber());
        return result;
    }

    @Override
    public UploadVersionProductCheckRsp checkUploadProductInfo(UploadVersionProductCheck check) {
        VersionManagement version = versionRepository.getById(check.getVersionId());
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(version.getSubSystemId());
        String productVersion = version.getVersionNumber();
        if(check.getFormat().equals("docker")){
            productVersion = version.getTotalVersionNumber();
        }
        DevopsProductMetadataDto devopsProductMetadataDto = productRepository.get(check.getRepoId(), check.getProductName(), productVersion);
        if(devopsProductMetadataDto == null){
            // 制品不存在
            return new UploadVersionProductCheckRsp(0);
        }
        ListInstanceReq req = new ListInstanceReq();
        req.setSystemId(devopsSubsystem.getSystemId());
        req.setProductIdList(Lists.newArrayList(devopsProductMetadataDto.getId()));
        List<PromotionInstanceDto> promotionInstanceDtos = productRepository.listPromotionInstance(req);
        if(CollectionUtils.isNotEmpty(promotionInstanceDtos) && CollectionUtils.isNotEmpty(promotionInstanceDtos.get(0).getPromotionNodeInstanceList())){
            if(promotionInstanceDtos.get(0).getPromotionNodeInstanceList().get(0).getPromotionStatus() != 0){
                // 制品已晋级
                return new UploadVersionProductCheckRsp(1);
            }
        }
        // 已存在，未晋级
        return new UploadVersionProductCheckRsp(2);
    }

    private List<DevopsRepository> selectUploadRepository(List<PromotionStrategyDTO> promotionStrategyDTOS, List<DevopsRepository> devopsRepositories, String format) {
        List<PromotionStrategyDTO> filterStrategy = promotionStrategyDTOS.stream().filter(stra -> stra.getFormat().equals(format)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filterStrategy) || CollectionUtils.isEmpty(filterStrategy.get(0).getPromotionNodes())){
            return devopsRepositories.stream().filter(repo -> repo.getFormat().equals(format)).collect(Collectors.toList());
        }else{
            PromotionNodeDTO promotionNodeDTO = filterStrategy.get(0).getPromotionNodes().get(0);
            DevopsRepository devopsRepository = new DevopsRepository();
            devopsRepository.setId(promotionNodeDTO.getRepoId());
            devopsRepository.setName(promotionNodeDTO.getRepoName());
            devopsRepository.setFormat(filterStrategy.get(0).getFormat());
            return Lists.newArrayList(devopsRepository);
        }
    }

    @Override
    public VersionDetails openedDetails(Long majorVersionId) {
        VersionManagement opened = versionRepository.getOpened(majorVersionId);
        VersionDetails details = details(opened.getId());
        return details;
    }

    @Override
    public VersionDeployInfo versionDeploy(Long subsystemId, String majorVersionNumber, List<Long> envIds, String productFormat) {
        List<MajorVersion> majorVersions = majorVersionRepository.listByParams(subsystemId, majorVersionNumber);
        if(CollectionUtils.isEmpty(majorVersions)){
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "版本信息不存在");
        }
        MajorVersion majorVersion = majorVersions.get(0);
        VersionManagement opened = versionRepository.getOpened(majorVersion.getId());
        if(opened == null){
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "版本信息错误：无开启子版本");
        }
        DevopsSubSystem subsystem = subsystemRepository.getById(opened.getSubSystemId());
        // 版本号
        VersionDeployInfo result = new VersionDeployInfo();
        result.setMajorVersion(majorVersionNumber);
        result.setVersion(opened.getTotalVersionNumber());

        // 配置库
        List<VersionComponents> versionComponents = versionComponentsRepository.listByParams(opened.getId(), null, null);
        List<Long> configRepo = versionComponents.stream().filter(c -> c.getComponent().equals(SystemConstance.VersionComponent.DEPLOY_CONFIG_REPO)).map(c -> Long.parseLong(c.getComponentKey())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(configRepo)){
            return result;
        }
        result.setConfigId(configRepo.get(0));

        // 配置库环境
        List<VersionComponents> versionConfigEnvs = versionComponents.stream().filter(c -> c.getComponent().equals(SystemConstance.VersionComponent.DEPLOY_CONFIG_ENV)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(versionConfigEnvs)){
            return result;
        }
        List<DoDeployEnv> doDeployEnvs = deployEnvRepository.listBySystem(subsystem.getSystemId());
        Map<String, List<DoDeployEnv>> envGroup = doDeployEnvs.stream().collect(Collectors.groupingBy(DoDeployEnv::getEnvCode));
        Map<Long, DoDeployEnv> envMap = doDeployEnvs.stream().collect(Collectors.toMap(DoDeployEnv::getId, e -> e));
        List<ProductConfigDTO> versionDeployEnvs = versionConfigEnvs.stream().map(env -> {
            ProductConfigDTO envInfo = new ProductConfigDTO();
            envInfo.setConfigEnvName(env.getComponentKey());
            List<DoDeployEnv> doDeployEnvs1 = envGroup.get(env.getComponentKey());
            if(CollectionUtils.isNotEmpty(doDeployEnvs1)){
                envInfo.setEnvId(doDeployEnvs1.get(0).getId());
            }
            VersionComponents commit = versionComponentsRepository.getSubordination(opened.getId(), SystemConstance.VersionComponent.DEPLOY_CONFIG_COMMIT, env.getId());
            if (commit != null) {
                envInfo.setConfigEnvCommit(commit.getComponentKey());
            }
            return envInfo;
        }).filter(e -> e.getConfigEnvCommit() != null && (CollectionUtils.isEmpty(envIds) || envIds.stream().filter(envId -> envMap.containsKey(envId)).map(envId -> envMap.get(envId).getEnvCode()).collect(Collectors.toList()).contains(e.getConfigEnvName()))).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(versionDeployEnvs)){
            return result;
        }

        // 配置库环境的制品
        List<DevopsProductMetadataDto> devopsProductMetadataDtos = productRepository.productList(subsystemId, opened.getTotalVersionNumber(), productFormat, null);
        List<Long> repoIds = devopsProductMetadataDtos.stream().map(dto -> dto.getRepoId()).collect(Collectors.toList());
        List<DevopsRepository> devopsRepositories = systemRepoRepository.listByIds(repoIds);
        for (ProductConfigDTO productConfigDTO : versionDeployEnvs) {
            if(productConfigDTO.getEnvId() == null){
                continue;
            }
            List<Long> filterRepo = devopsRepositories.stream().filter(r -> r.getEnvId() != null && r.getEnvId().equals(productConfigDTO.getEnvId())).map(r -> r.getId()).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(filterRepo)){
                List<DevopsProductMetadataDto> collect = devopsProductMetadataDtos.stream().filter(dto -> filterRepo.contains(dto.getRepoId())).collect(Collectors.toList());
                productConfigDTO.setProducts(collect);
            }
        }
        result.setVersionEnvs(versionDeployEnvs);
        return result;
    }

    @Override
    public MajorVersion findByVersionNum(Long subSystemId,String version) {
        MajorVersion one = majorVersionRepository.getOne(Wrappers.lambdaQuery(MajorVersion.class).eq(MajorVersion::getSubSystemId, subSystemId).eq(MajorVersion::getVersionNumber, version));
        if(ObjectUtil.isEmpty(one)){
            throw new BusinessException("找不到对应的版本信息");
        }
        return one;
    }
}