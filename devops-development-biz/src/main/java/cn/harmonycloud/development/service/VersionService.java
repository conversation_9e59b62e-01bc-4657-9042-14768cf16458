package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.feature.FeatureIssuesDto;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.system.DevopsSystemDto;
import cn.harmonycloud.development.pojo.entity.*;
import cn.harmonycloud.development.pojo.vo.pipeline.VersionDeployInfo;
import cn.harmonycloud.development.pojo.vo.repository.ProductVo;
import cn.harmonycloud.development.pojo.vo.repository.PromotionNodeInstanceVo;
import cn.harmonycloud.development.pojo.vo.version.*;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pojo.version.MajorVersionDto;
import cn.harmonycloud.pojo.version.VersionDto;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * @description: 针对表【version_management】
 * @author: yx
 * @time: 2022/8/22
 */
public interface VersionService {

    /**
     * 创建大版本
     * @param request
     * @return
     */
    MajorVersion createVersion(CreateVersionRequest request);

    VersionManagement preIteration(Long subVersionId);

    /**
     * 版本迭代
     *
     * @param request
     * @return
     */
    VersionManagement versionIteration(VersionIterationRequest request);

    //选择版本
    VersionVo selectVersion(SelectVersionVo request);

    void updateVersion(UpdateVersionVo request);

    void deleteVersion(DeleteVersionVo request);


    void promote(ProductPromotionVo request);

    void saveVersionInstance(VersionInstance instance);

    void saveVersionInstance(Long subsystemId, Long jobId, Long buildId, Long versionId);


    List<MyVersionVO> myVersion(Long subSystemId);

    /**
     * 大版本分页
     *
     * @param request
     * @return
     */
    Page<MajorVersionDto> page(MajorVersionQuery request);

    /**
     * 版本详情
     *
     * @param id
     * @return
     */
    VersionDetails details(Long id);

    /**
     * 版本特性列表
     *
     * @param id
     * @return
     */
    List<FeatureIssuesDto> featureQuery(Long id);

    /**
     * 添加版本特性
     *
     * @param modify
     */
    void featureAdd(VersionFeatureModify modify);

    /**
     * 将特性转为版本组件对象
     *
     * @param version
     * @param featureIds
     * @param currentUser
     * @return
     */
    List<VersionComponents> getFeatureComponents(VersionManagement version, List<Long> featureIds, User currentUser);


    /**
     *制品列表
     *
     * @param id
     * @return
     */
    List<DevopsProductMetadataDto> productList(Long id, String format);

    /**
     * 版本部署配置
     *
     * @param id
     * @return
     */
    VersionDeployVo deployConfig(Long id);

    /**
     * 保存部署配置
     *
     * @param config
     */
    void saveDeployConfig(VersionDeployConfig config);

    /**
     * 删除部署配置
     *
     * @param versionId
     */
    void removeDeployConfig(RemoveVersionConfig versionId);

    VersionDetails last(Long subsystemId);

    List<MajorVersionDto> list(VersionGeneralListRequest request);

    List<DevopsSystemDto> systemVersionList(VersionSubsystemQuery versionSubsystemQuery);

    List<VersionDetailsVo> getVersionAll(Long subSystemId);


    List<VersionDto> versionList(Long majorVersionId);

    void updateMajorVersion(UpdateMajorVersionVo request);


    void changeVersionStatus(ChangeVersionSwitchStatusRequest request);

    List<MajorVersionDto> majorVersionList(Long subSystemId);

    List<String> envList(Long configId,Long versionId);

    /**
     * 获取版本制品以及晋级流程记录
     *
     * @param versionId
     * @return
     */
    List<ProductVo> listProduct(Long versionId);

    /**
     * 删除主版本
     *
     * @param request
     */
    void deleteMajorVersion(DeleteMajorVersionReq request);

    /**
     * 上传版本制品信息
     *
     * @param versionId
     * @return
     */
    UploadVersionProductVo getUploadProductInfo(Long versionId);

    /**
     * 上传制品校验
     *
     * @param check
     * @return
     */
    UploadVersionProductCheckRsp checkUploadProductInfo(UploadVersionProductCheck check);

    /**
     * 查询开启子版本详情
     * @param majorVersionId
     * @return
     */
    VersionDetails openedDetails(Long majorVersionId);

    /**
     * 查询应用版本部署信息
     *
     * @param subsystemId
     * @param majorVersion
     * @param envId
     * @param productFormat
     * @return
     */
    VersionDeployInfo versionDeploy(Long subsystemId, String majorVersion, List<Long> envId, String productFormat);

    MajorVersion findByVersionNum(Long subSystemId,String version);
}