package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.util.PageUtils;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.system.*;
import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;
import cn.harmonycloud.development.pojo.entity.SystemProject;
import cn.harmonycloud.development.service.SubSystemService;
import cn.harmonycloud.development.service.SystemMemberService;
import cn.harmonycloud.development.service.mapstruct.DevopsSystemMapstruct;
import cn.harmonycloud.enums.DevopsLabelEnum;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.pojo.entity.SystemComponent;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.service.SystemService;
import cn.harmonycloud.development.service.SystemComponentService;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.Tag;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.UserVo;
import cn.harmonycloud.pojo.system.SystemQuery;
import cn.harmonycloud.tenant.TenantContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【hzbank_system(系统表)】的数据库操作Service实现
 * @createDate 2022-07-26 16:27:56
 */
@Service
public class SystemServiceImpl implements SystemService {

    @Autowired
    private DevopsSystemRepository devopsSystemRepository;
    @Autowired
    private SystemComponentService systemComponentService;
    @Autowired
    private IamRepository iamRepository;
    @Autowired
    private DevopsSystemMapstruct devopsSystemMapstruct;
    @Autowired
    private CodeGroupRepository codeGroupRepository;
    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private FeatureRepository featureRepository;
    @Autowired
    private DevopsLabelRepository devopsLabelRepository;
    @Autowired
    private SystemMemberService systemMemberService;
    @Autowired
    private SystemProjectRepository systemProjectRepository;

    @Value("${biz.regex.systemName:^(?![-_])(?!.*?[-_]$)[a-zA-Z0-9_\\u4e00-\\u9fa5-]{1,100}$}")
    private String systemNameRegex;

    @Value("${biz.regex.systemCode:^(?!.*\\.(git|atom)$)[a-zA-Z0-9][a-zA-Z0-9._-]{3,48}[a-zA-Z0-9]$}")
    private String systemCodeRegex;

    @Autowired
    private SubSystemService subSystemService;
    @Autowired
    private SystemRepoRepository systemRepoRepository;

    @Override
    public List<DevopsSystem> listSystemByUser(String permissionCode) {
        List<ResourceInstance> resourceInstances = permissionRepository.resourceList(SystemConstance.AmpResourceTypeCode.SYSTEM);
        if (CollectionUtils.isEmpty(resourceInstances)) {
            return new ArrayList<>();
        }
        List<Long> systemIds = resourceInstances.stream().map(ri -> ri.getResourceInstanceId()).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(permissionCode)) {
            Map<Long, Map<String, Boolean>> longMapMap = permissionRepository.mapPermission(SystemConstance.AmpResourceTypeCode.SYSTEM, systemIds);
            systemIds = systemIds.stream().filter(systemId -> {
                Map<String, Boolean> stringBooleanMap = longMapMap.getOrDefault(systemId, new HashMap<>());
                return stringBooleanMap.getOrDefault(permissionCode, false);
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(systemIds)) {
                return new ArrayList<>();
            }
        }
        return devopsSystemRepository.listByIds(systemIds);
    }

    @Override
    public DevopsSystem systemCode(String systemCode) {
        List<DevopsSystem> list = devopsSystemRepository.listByParams(null, systemCode);
        if (CollectionUtils.isEmpty(list)) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "系统信息不存在");
        }
        return list.get(0);
    }

    @Override
    public List<GroupDto> groups() {
        List<GroupDto> groups = codeGroupRepository.getGroups();
        if (CollectionUtils.isEmpty(groups)) {
            return new ArrayList<>();
        }
        List<SystemComponent> allGitlabGroup = systemComponentService.getAllGitlabGroup();
        if (CollectionUtils.isEmpty(allGitlabGroup)) {
            return groups;
        }
        Map<String, SystemComponent> collect = allGitlabGroup.stream().distinct().collect(Collectors.toMap(SystemComponent::getComponentKey, c -> c));
        return groups.stream().filter(group -> !collect.containsKey(group.getId().toString())).collect(Collectors.toList());
    }

    @Override
    public List<SystemStatisticsVo> statisticsFeature(Long systemId) {
        List<UserVo> userInfoDtoList = iamRepository.resourceMember(SystemConstance.AmpResourceTypeCode.SYSTEM, systemId);
        if (CollectionUtils.isEmpty(userInfoDtoList)) {
            return new ArrayList<>();
        }
        List<DevopsFeature> list = featureRepository.listBySystemId(systemId);
        Map<Long, List<DevopsFeature>> featureGroup = list.stream().filter(devopsFeature -> devopsFeature.getDirector() != null)
                .collect(Collectors.groupingBy(DevopsFeature::getDirector));
        List<SystemStatisticsVo> collect = userInfoDtoList.stream().map(user -> {
            SystemStatisticsVo feature = new SystemStatisticsVo();
            feature.setUserId(user.getId());
            feature.setName(user.getName());
            if (CollectionUtils.isNotEmpty(user.getRoles())) {
                feature.setRoleName(user.getRoles().get(0).getName());
            }
            List<DevopsFeature> userFeature = featureGroup.getOrDefault(user.getId(), new ArrayList<>());
            List<DevopsFeature> listFeatureDtoStream = userFeature.stream().filter(f -> f.getFeatureStatus() != 0).collect(Collectors.toList());
            feature.setSumFeature(userFeature.size());
            feature.setFinishFeature(listFeatureDtoStream.size());
            return feature;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public Page<DevopsSystemDto> page(SystemPageRequest request) {
        List<Long> resourceSystemIds = getResourceSystemIds();
        if (CollectionUtils.isEmpty(resourceSystemIds)) {
            return new Page<>(request.getPageNo(), request.getPageSize());
        }
        if (CollectionUtils.isNotEmpty(request.getLabels())) {
            // 标签过滤
            List<Long> instanceIds = devopsLabelRepository.listInstanceId(DevopsLabelEnum.SYSTEM.getClassificationCode(), request.getLabels());
            if (CollectionUtils.isEmpty(instanceIds)) {
                return new Page<>(request.getPageNo(), request.getPageSize());
            }
            resourceSystemIds = resourceSystemIds.stream().filter(instanceIds::contains).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(request.getProjectIds())) {
            // 项目过滤
            List<Long> instanceIds = systemProjectRepository.listSystemByParams(request.getProjectIds());
            if (CollectionUtils.isEmpty(instanceIds)) {
                return new Page<>(request.getPageNo(), request.getPageSize());
            }
            resourceSystemIds = resourceSystemIds.stream().filter(instanceIds::contains).collect(Collectors.toList());
        }
        if (resourceSystemIds.size() == 0) {
            return new Page<>(request.getPageNo(), request.getPageSize());
        }
        SystemPageQuery query = new SystemPageQuery();
        query.setIds(resourceSystemIds);
        query.setSubFullNameCn(request.getSubFullNameCn());
        query.setSysCode(request.getSysCode());
        query.setProjectDirectorIds(request.getProjectDirectorIds());
        query.setPageNo(request.getPageNo());
        query.setPageSize(request.getPageSize());
        query.setTop(request.getTop());
        return pageDto(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DevopsSystem create(SystemCreateRequest request) {
        checkCreateRequestParam(request);
        User currentUser = iamRepository.getCurrentUser();
        DevopsSystem devopsSystem = saveLocal(request, currentUser);
        systemProjectRepository.save(devopsSystem.getId(), request.getProjectIds());
        systemComponentService.addGroupId(devopsSystem.getId(), devopsSystem.getSubFullNameCn(), devopsSystem.getSysDescCn(), devopsSystem.getSysCode(), request.getGitGroupId());
        permissionRepository.resourceInstances(SystemConstance.AmpResourceTypeCode.SYSTEM, devopsSystem.getId(), devopsSystem.getSubFullNameCn(), null, null);
        addAdminRole(devopsSystem, currentUser);
        return devopsSystem;
    }

    @Override
    public SystemDetails details(Long id) {
        DevopsSystem devopsSystem = devopsSystemRepository.getById(id);
        if (devopsSystem == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        SystemDetails details = devopsSystemMapstruct.toDevopsSystemDetails(devopsSystem);
        GroupDto gitlabGroup = systemComponentService.getGitlabGroup(details.getId());
        if (gitlabGroup == null) {
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "数据异常：代码分组不存在");
        }
        List<Long> labels = devopsLabelRepository.listLabelByInstanceId(DevopsLabelEnum.SYSTEM.getClassificationCode(), details.getId());
        List<Long> projectIds = systemProjectRepository.listProjectByParams(id, null);
        details.setCodeGroup(gitlabGroup);
        details.setLabels(labels);
        details.setProjectIds(projectIds);
        return details;

    }

    @Override
    public DevopsSystem modify(SystemUpdateRequest request) {
        DevopsSystem oldData = devopsSystemRepository.getById(request.getId());
        User currentUser = iamRepository.getCurrentUser();
        if (oldData == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        preUpdateIssues(request);
        // 更新代码分组名称
        if (StringUtils.isNotEmpty(request.getSubFullNameCn()) && !StringUtils.equals(request.getSubFullNameCn(), oldData.getSubFullNameCn())) {
            systemComponentService.postUpdateSystem(oldData.getId(), request.getSubFullNameCn());
        }
        DevopsSystem devopsSystem = modifyLocal(request, currentUser);
        // 更新标签
        devopsLabelRepository.removeAndSave(DevopsLabelEnum.SYSTEM.getClassificationCode(), request.getId(), request.getLabels());
        // 更新系统、项目关联关系
        systemProjectRepository.save(devopsSystem.getId(), request.getProjectIds());
        // 更新应用管理资源名称
        permissionRepository.updateResourceInstances(SystemConstance.AmpResourceTypeCode.SYSTEM, request.getId(), request.getSubFullNameCn());
        // 更新管理员信息
        updateAdminRole(oldData, request);
        return devopsSystem;
    }

    @Transactional
    @Override
    public void remove(Long id) {
        User currentUser = iamRepository.getCurrentUser();
        devopsSystemRepository.removeLogic(id, currentUser);
        permissionRepository.deleteResourceInstances(SystemConstance.AmpResourceTypeCode.SYSTEM, id);
        systemComponentService.deleteComponent(id);
        systemRepoRepository.unBind(id);
        subSystemService.postRemoveSystem(id);
    }

    @Override
    public List<DevopsSystemDto> list(Long projectId) {
        List<DevopsSystem> systemList = devopsSystemRepository.listByProject(projectId);
        return systemList.stream().map(system -> devopsSystemMapstruct.toDevopsSystemDto(system)).collect(Collectors.toList());
    }

    @Override
    public void top(TopRequest topRequest) {
        Long systemId = topRequest.getId();
        User currentUser = iamRepository.getCurrentUser();
        if (topRequest.isTop()) {
            devopsSystemRepository.topById(systemId, currentUser);
        } else {
            devopsSystemRepository.closeTop(systemId, currentUser);
        }
    }

    @Override
    public List<DevopsSystemDto> listAll(SystemQuery query) {
        if (query.getOrganId() != null) {
            TenantContextHolder.setTenantId(query.getOrganId().toString());
        }
        List<DevopsSystem> systems;
        if(CollectionUtils.isNotEmpty(query.getIds())){
            systems = devopsSystemRepository.listByParams(query.getIds());
        }else {
            systems = devopsSystemRepository.list();
        }
        List<Long> systemIds = systems.stream().map(s -> s.getId()).collect(Collectors.toList());
        Map<Long, SystemComponent> longSystemComponentMap = systemComponentService.mapBySystemIds(systemIds);
        return systems.stream().map(system -> {
            DevopsSystemDto devopsSystemDto = devopsSystemMapstruct.toDevopsSystemDto(system);
            if (longSystemComponentMap.containsKey(system.getId())) {
                SystemComponent systemComponent = longSystemComponentMap.get(system.getId());
                devopsSystemDto.setGroupId(Integer.parseInt(systemComponent.getComponentKey()));
            }
            return devopsSystemDto;
        }).collect(Collectors.toList());
    }

    @Override
    public SystemDataVO getProjects(SystemDataVO systemDataVO) {
        if(systemDataVO == null) {
            return null;
        }
        List<Long> projectIds = systemProjectRepository.listProjectByParams(systemDataVO.getId(), null);
        systemDataVO.setProjectIds(projectIds);
        return systemDataVO;
    }

    @Override
    public void bindProject(SystemProjectRequest systemProjectRequest) {
        if(CollectionUtils.isEmpty(systemProjectRequest.getSystemIds())) {
            return;
        }
//        systemProjectRequest.getSystemIds().stream().forEach(systemId -> {
//            List<Long> projectIds = systemProjectRepository.listProjectByParams(systemId, null);
//            if(CollectionUtils.isNotEmpty(projectIds)) {
//                throw new BusinessException("系统已关联其他项目，请先解除关联");
//            }
//        });
        systemProjectRequest.getSystemIds().stream().forEach(systemId -> {
            systemProjectRepository.save(systemId, Arrays.asList(systemProjectRequest.getProjectId()));
        });
    }

    @Override
    public void unbindProject(SystemProjectRequest systemProjectRequest) {
        systemProjectRequest.getSystemIds().stream().forEach(systemId -> {
            systemProjectRepository.remove(new LambdaQueryWrapper<SystemProject>().eq(SystemProject::getSystemId, systemId).eq(SystemProject::getProjectId, systemProjectRequest.getProjectId()));
        });
    }

    @Override
    public List<SystemDataVO> getUnboundProjectSystem(List<SystemDataVO> systemDataVOList) {
        List<Long> systemIdList = systemDataVOList.stream().map(SystemDataVO::getId).collect(Collectors.toList());
        List<SystemProject> systemProjectList = systemProjectRepository.list(new LambdaQueryWrapper<SystemProject>().in(SystemProject::getSystemId, systemIdList));
        Map<Long, List<Long>> map = systemProjectList.stream().collect(Collectors.groupingBy(SystemProject::getSystemId, Collectors.mapping(SystemProject::getProjectId, Collectors.toCollection(ArrayList::new))));
        List<SystemDataVO> unboundSystemList = systemDataVOList.stream().filter(systemDataVO -> map.get(systemDataVO.getId()) == null).collect(Collectors.toList());
        return unboundSystemList;
    }


    private DevopsSystem modifyLocal(SystemUpdateRequest request, User currentUser) {
        DevopsSystem data = devopsSystemMapstruct.toDevopsSystem(request);
        data.setUpdateBy(currentUser.getId());
        data.setUpdateTime(LocalDateTime.now());
        devopsSystemRepository.updateById(data);
        return devopsSystemRepository.getById(request.getId());
    }

    public void preUpdateIssues(SystemUpdateRequest request) {
        // 空格和重名校验，长度校验
        checkParam(request.getSubFullNameCn(), null, request.getSysDescCn());
        List<DevopsSystem> systemList = devopsSystemRepository.listByParams(request.getSubFullNameCn());
        if (CollectionUtils.isNotEmpty(systemList) && !systemList.get(0).getId().equals(request.getId())) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "系统名称已存在");
        }
    }

    private void updateAdminRole(DevopsSystem oldData, SystemUpdateRequest request) {
        if (request.getProjectDirectorId() == null) {
            return;
        }
        List<RoleInfoDto> roleInfoDto = systemMemberService.systemRoles();
        for (RoleInfoDto infoDto : roleInfoDto) {
            if (SystemConstance.SystemRoleCode.SYS_ADMIN.equals(infoDto.getCode())) {
                SaveMemberDto memberDto = new SaveMemberDto();
                memberDto.setUserId(request.getProjectDirectorId());
                memberDto.setRoleId(infoDto.getId());
                memberDto.setInstanceId(request.getId());
                systemMemberService.saveMember(memberDto);
            }
        }
    }

    private void addAdminRole(DevopsSystem devopsSystem, User currentUser) {
        RoleInfoDto roleInfoDto = systemMemberService.systemAdminRoles();
        Long directorId = devopsSystem.getProjectDirectorId();
        CreateMemberVO createMemberVO = new CreateMemberVO();
        List<Long> userIds = new ArrayList<>();
        Long currentId = currentUser.getId();
        userIds.add(directorId);
        if (!currentId.equals(directorId)) {
            userIds.add(currentId);
        }
        createMemberVO.setRoleId(roleInfoDto.getId());
        createMemberVO.setInstanceId(devopsSystem.getId());
        createMemberVO.setUserIds(userIds);
        createMemberVO.setSyncCodeRepo(true);
        systemMemberService.createMember(createMemberVO);
    }

    private DevopsSystem saveLocal(SystemCreateRequest request, User currentUser) {
        LocalDateTime now = LocalDateTime.now();
        DevopsSystem devopsSystem = devopsSystemMapstruct.toDevopsSystem(request);
        devopsSystem.setDelFlag(SystemConstance.NOT_DELETE);
        devopsSystem.setCreateBy(currentUser.getId());
        devopsSystem.setUpdateBy(currentUser.getId());
        devopsSystem.setCreateTime(now);
        devopsSystem.setUpdateTime(now);
        devopsSystem.setTopSort(0);
        devopsSystemRepository.save(devopsSystem);
        return devopsSystem;
    }

    private void checkParam(String systemName, String sysCode, String description) {
        if (!Pattern.matches(systemNameRegex, systemName)) {
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "系统名称只能由中英文、数字、字母开头，由1-100位中英文、数字、字母、中划线和下划线组成");
        }

        if (sysCode != null && !Pattern.matches(systemCodeRegex, sysCode)) {
            throw new SystemException(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID, "系统编码格式错误：只能由数字、字母开头和结尾,5-50位数字、字母、'.'、下划线和中横线组成且结尾不能是.git或.atom");
        }
        if (StringUtils.isNotEmpty(description) && description.length() > 255) {
            throw new SystemException(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID, "系统介绍不能超过255个字符");
        }
    }

    public void checkCreateRequestParam(SystemCreateRequest request) {
        checkParam(request.getSubFullNameCn(), request.getSysCode(), request.getSysDescCn());
        List<DevopsSystem> devopsSystems = devopsSystemRepository.listByParams(null, request.getSysCode());
        if (CollectionUtils.isNotEmpty(devopsSystems)) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "系统code已存在");
        }
        devopsSystems = devopsSystemRepository.listByParams(request.getSubFullNameCn());
        if (CollectionUtils.isNotEmpty(devopsSystems)) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "系统名称已存在");
        }
        Integer gitGroup = request.getGitGroupId();
        if (gitGroup != null && gitGroup > 0) {
            // 关联校验
            List<SystemComponent> allGitlabGroup = systemComponentService.getAllGitlabGroup(gitGroup);
            if (CollectionUtils.isNotEmpty(allGitlabGroup)) {
                List<Long> systemIds = allGitlabGroup.stream().map(c -> c.getSystemId()).collect(Collectors.toList());
                List<DevopsSystem> systems = devopsSystemRepository.listByParamsNoTenant(systemIds);
                List<String> collect = systems.stream().map(s -> s.getSubFullNameCn()).collect(Collectors.toList());
                String join = Joiner.on(",").join(collect);
                throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "代码仓库组已被 " + join + " 使用，请重新选择其他分组");
            }
        } else {
            // 新建校验
            GroupDto group = codeGroupRepository.group(null, request.getSysCode());
            if (group != null) {
                List<SystemComponent> allGitlabGroup = systemComponentService.getAllGitlabGroup(group.getId());
                if (CollectionUtils.isEmpty(allGitlabGroup)) {
                    throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "代码仓库组 “" + request.getSysCode() + "“已存在，请选择已有分组或修改系统编码");
                } else {
                    List<Long> systemIds = allGitlabGroup.stream().map(c -> c.getSystemId()).collect(Collectors.toList());
                    List<DevopsSystem> systems = devopsSystemRepository.listByParamsNoTenant(systemIds);
                    List<String> collect = systems.stream().map(s -> s.getSubFullNameCn()).collect(Collectors.toList());
                    String join = Joiner.on(",").join(collect);
                    throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "代码仓库组 “" + request.getSysCode() + "“已被系统“" + join + "“使用，请修改系统编码");
                }
            }
        }
    }

    private Page<DevopsSystemDto> pageDto(SystemPageQuery query) {
        Page<DevopsSystem> data = devopsSystemRepository.pageGeneral(query);
        List<DevopsSystem> records = data.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageUtils.exchangeRecordData(data, new ArrayList<>());
        }
        // 标签集合
        List<Long> systemIds = records.stream().map(system -> system.getId()).collect(Collectors.toList());
        Map<Long, List<Tag>> longListMap = devopsLabelRepository.mapLabelByInstanceId(DevopsLabelEnum.SYSTEM.getClassificationCode(), systemIds);
        // 负责人集合
        List<Long> directorIds = records.stream().filter(system -> system.getProjectDirectorId() != null).map(system -> system.getProjectDirectorId()).collect(Collectors.toList());
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(directorIds);
        return PageUtils.exchangeRecord(data, system -> {
            DevopsSystemDto devopsSystemDto = devopsSystemMapstruct.toDevopsSystemDto(system);
            if (devopsSystemDto.getProjectDirectorId() != null && userMap.containsKey(devopsSystemDto.getProjectDirectorId())) {
                User user = userMap.get(devopsSystemDto.getProjectDirectorId());
                devopsSystemDto.setProjectDirectorName(user.getName());
            }
            List<Tag> orDefault = longListMap.get(devopsSystemDto.getId());
            if (orDefault == null) {
                orDefault = new ArrayList<>();
            }
            devopsSystemDto.setLabels(orDefault);
            return devopsSystemDto;
        });
    }

    private List<Long> getResourceSystemIds() {
        List<ResourceInstance> resources = permissionRepository.resourceList(SystemConstance.AmpResourceTypeCode.SYSTEM);
        return resources.stream().map(resource -> resource.getResourceInstanceId()).collect(Collectors.toList());
    }


}