package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.system.EnvTestListDTO;
import cn.harmonycloud.development.pojo.dto.system.VersionTestDTO;
import cn.harmonycloud.development.pojo.dto.test.*;
import cn.harmonycloud.development.pojo.dto.system.EnvTestDTO;
import cn.harmonycloud.development.pojo.entity.TestManagement;
import cn.harmonycloud.development.pojo.vo.autotest.ResultItemVO;
import cn.harmonycloud.development.pojo.vo.test.*;
import cn.harmonycloud.project.model.dto.IssuesDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface TestManagementService {

    /**
     * 子系统工作台-测试进度，最多返回10条
     * @param subsystemId 子系统id
     * @param stageEnvId 阶段环境id
     * @return
     */
    List<MyTestVO> myTest(Long subsystemId, Long stageEnvId);

    /**
     * 环境测试记录-sit/uat
     * @param envTestDTO
     */
    Page<EnvTestVO> getEnvTestRecords(EnvTestDTO envTestDTO);

    void create(CreateTestRequest createTestRequest);

    /**
     * 测试管理--环境测试列表
     * @param envTestListDTO
     * @return
     */
    Page<EnvTestListVO> envTestList(EnvTestListDTO envTestListDTO);

    Page<EnvTestVersionVO> getVersionTestRecords(EnvTestDTO envTestDTO);

    ResultItemVO resultList(Long id);

    /**
     * 版本测试-状态改变
     * @param id
     * @param status
     */
    Boolean updateStatus(Long id, Integer status);

    /**
     * 转派测试人员
     * @param id
     * @param directorId
     * @return
     */
    Boolean updateDirector(Long id, Long directorId);

    /**
     * 保存测试报告
     * @param testReportUrls
     * @return
     */
    Boolean saveTestReport(String ids, Integer testResult, String testReportUrls,String testResultDescription);

    List<TestManagement> list(TestManagementQuery query);

    /**
     * 查询当前环境指定流水线下构建的任务列表
     * @param stageEnvId 阶段环境id
     * @param jobId 任务id
     * @return
     */
    List<IssuesDTO> issues(Long stageEnvId, Long jobId);

    /**
     * 2.0版本发起提测功能
     * @param createTestRequest
     */
    void save(TestCreateRequest createTestRequest);

    /**
     * 发起测试获取提测的数据
     * @param createTestRequest
     */
    TestPreCreateResponse preCreate(TestPreCreateRequest createTestRequest);

    /**
     * 测试单详情
     * @param id
     * @return
     */
    TestManagementDetails detail(Long id);

    void modify(TestModifyRequest modifyRequest);

    void batchModify(TestBatchModifyRequest modifyRequest);
}
