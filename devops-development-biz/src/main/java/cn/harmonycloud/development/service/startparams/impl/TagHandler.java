package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/8 4:41 下午
 **/
@Component
public class TagHandler implements RunStartParamsHandler {


    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.TAG;
    }

    @Override
    public void setRunValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        if(context.getVersionManagement() != null){
            startParameter.setValue(context.getVersionManagement().getVersionNumber());
        }
    }
}
