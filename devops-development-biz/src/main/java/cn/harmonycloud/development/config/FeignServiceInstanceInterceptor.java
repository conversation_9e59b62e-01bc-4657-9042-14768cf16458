package cn.harmonycloud.development.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.List;

@Slf4j
@Component
public class FeignServiceInstanceInterceptor implements RequestInterceptor {

    @Autowired
    private DiscoveryClient discoveryClient;

    @Override
    public void apply(RequestTemplate template) {
        URI uri = URI.create(template.feignTarget().url());
        String host = uri.getHost();
        
        // 检查是否是服务名
        if (!host.contains(".")) {
            log.info("Feign 请求服务: {}", host);
            
            // 使用 DiscoveryClient 获取服务实例
            List<ServiceInstance> instances = discoveryClient.getInstances(host);
            log.info("服务 [{}] 实例数量: {}", host, instances.size());
            
            for (ServiceInstance instance : instances) {
                log.info("服务实例: {} - {}:{} - 元数据: {}", 
                        instance.getInstanceId(), 
                        instance.getHost(), 
                        instance.getPort(), 
                        instance.getMetadata());
            }
        }
    }
}