package cn.harmonycloud.development.config;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executor;

/**
 *@Author: wutx
 *@CreateTime: 2024-07-18  09:22
 *@Description: TODO
 *@Version: 1.0
 */
@Configuration
@EnableAsync
public class AsyncConfiguration implements AsyncConfigurer {

    @Override
    public Executor getAsyncExecutor() {
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        return ExecutorBuilder.create()
                .setCorePoolSize(availableProcessors + 1)
                .setMaxPoolSize(availableProcessors + 1)
                .useArrayBlockingQueue(1024)
                .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("async-").build())
                .build();
    }
}

