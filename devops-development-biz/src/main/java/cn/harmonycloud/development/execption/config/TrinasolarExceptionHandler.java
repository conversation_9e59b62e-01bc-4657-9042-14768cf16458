package cn.harmonycloud.development.execption.config;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.exception.TrinasolarException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice(basePackages = "cn.harmonycloud.development.inbound.controller.trinasolar")
public class TrinasolarExceptionHandler {

    @ExceptionHandler(Exception.class)
    public BaseResult<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return BaseResult.failed("系统异常:" + e.getMessage());
    }

    @ExceptionHandler(TrinasolarException.class)
    public BaseResult<Void> handleSystemException(TrinasolarException e) {
        return BaseResult.failed(e.getMessage());
    }

}