package cn.harmonycloud.development.execption.thgn;

import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.TrinasolarException;

/**
 * <AUTHOR>
 */
public class AppException extends TrinasolarException {


    public AppException(ExceptionCode exceptionCode) {
        super(exceptionCode);
    }

    public AppException(String message) {
        super(ExceptionCode.INNER_EXCEPTION, message);
    }

    public AppException(ExceptionCode exceptionCode, String msg) {
        super(exceptionCode, msg);
    }

    public AppException(ExceptionCode exceptionCode, int code, String msg) {
        super(exceptionCode, code, msg);
    }
}
