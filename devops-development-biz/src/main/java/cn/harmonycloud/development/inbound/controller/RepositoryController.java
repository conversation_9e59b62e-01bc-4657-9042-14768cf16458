package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.repository.DevopsRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.pojo.vo.repository.CreateRepositoryRequest;
import cn.harmonycloud.development.service.RepositoryService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description 系统制品库管理
 * <AUTHOR>
 * @Date 2023/4/4 2:16 下午
 **/
@Validated
@Api(tags = "系统制品库管理")
@RequestMapping("/repository")
@RestController
public class RepositoryController {


    @Autowired
    private RepositoryService repositoryService;

    @OperationAudit(operationName = "制品库-新增制品库:${repoName}",
            operationCode = "研发协同-制品库",
            dynOperationValues = {
                    @DynOperationValue(key = "repoName", jstl = "$[0].repoName")
            }
    )
    @ApiOperation("新建一个制品库")
    @PostMapping("/create")
    public BaseResult create(@Valid @RequestBody CreateRepositoryRequest request) {
        repositoryService.createRepository(request);
        return BaseResult.ok();
    }

    @ApiOperation("制品库列表")
    @GetMapping("/list")
    public BaseResult<List<DevopsRepository>> list(@RequestParam(required = false) Long configId,
                                                   @RequestParam(required = false) Long systemId,
                                                   @RequestParam(required = false) Long subsystemId,
                                                   @RequestParam(required = false) String format,
                                                   @RequestParam(required = false) String repoName,
                                                   @RequestParam(required = false) Boolean withPublicRepository,
                                                   @RequestParam(required = false) Boolean withProdRepository) {
        return BaseResult.ok(repositoryService.list(configId, systemId, subsystemId, format, repoName, withPublicRepository, withProdRepository));
    }

    @ApiOperation("环境列表（已创建仓库的环境）")
    @GetMapping("/envList")
    public BaseResult<List<DoDeployEnv>> envList(@RequestParam Long systemId) {
        List<DoDeployEnv> list = repositoryService.envList(systemId);
        return BaseResult.ok(list);
    }

    @OperationAudit(operationName = "制品库-删除制品库",
            operationCode = "研发协同-制品库"
    )
    @ApiOperation("删除制品库")
    @PostMapping("/remove/{id}")
    public BaseResult remove(@PathVariable Long id) {
        repositoryService.remove(id);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "制品库-制品晋级目标仓库",
            operationCode = "研发协同-制品库"
    )
    @ApiOperation("制品晋级目标仓库接口")
    @GetMapping("/targetRepository")
    public BaseResult<List<DevopsRepository>> targetRepository(@RequestParam Long systemId, @RequestParam String format, @RequestParam Long sourceRepoId) {
        List<DevopsRepository> result = repositoryService.targetRepository(systemId, format, sourceRepoId);
        return BaseResult.ok(result);
    }

}
