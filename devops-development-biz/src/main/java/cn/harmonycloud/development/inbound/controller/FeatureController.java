package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.FeatureBranchRepository;
import cn.harmonycloud.development.outbound.DevopsLabelRepository;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchStageDto;
import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.pojo.vo.feature.*;
import cn.harmonycloud.development.service.FeatureService;
import cn.harmonycloud.development.service.ProjectManagementService;
import cn.harmonycloud.enums.BranchModelEnum;
import cn.harmonycloud.enums.DevopsLabelEnum;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import cn.harmonycloud.project.model.dto.IssuesDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.*;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/3
 */
@Api(tags = "特性任务管理")
@RequestMapping("/feature")
@RestController
public class FeatureController {
    @Autowired
    private FeatureService featureService;
    @Autowired
    private ProjectManagementService projectManagementService;
    @Autowired
    private FeatureBranchRepository featureBranchRepository;
    @Autowired
    private DevopsLabelRepository devopsLabelRepository;

    @ApiOperation("查询特性列表（分页）")
    @GetMapping("/queryPage")
    public BaseResult<Page<DevopsFeatureDto>> page(FeaturePageRequest request) {
        return BaseResult.ok(featureService.pageFeature(request));
    }


    @ApiOperation("卡片列表")
    @GetMapping("/list")
    public BaseResult listCard(FeatureListRequest request) {
        List<DevopsFeatureDto> list = featureService.list(request);
        return BaseResult.ok(list);
    }

    @ApiOperation("特性分支列表（分支名称）")
    @GetMapping("/branches")
    public BaseResult<List<FeatureBranch>> branches(@ApiParam("子系统id") @RequestParam Long subsystemId) {
        List<FeatureBranch> list = featureBranchRepository.listByParam(subsystemId);
        return BaseResult.ok(list);
    }

    @OperationAudit(operationName = "标签管理-特性打标签",
            operationCode = "研发协同-标签"
    )
    @ApiOperation("特性打标签")
    @PostMapping("/tag")
    public BaseResult tag(@RequestBody FeatureTagRequest request) {
        featureService.featureTag(request);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "标签管理-特性标签删除",
            operationCode = "研发协同-标签"
    )
    @ApiOperation("取消标签")
    @PostMapping("/unTag")
    public BaseResult unTag(@RequestBody FeatureUnTagRequest request) {
        devopsLabelRepository.removeByParams(DevopsLabelEnum.FEATURE.getClassificationCode(), request.getFeatureId(), request.getLabelId());
        return BaseResult.ok();
    }

    @ApiOperation("特性分支比较")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "特性列表", paramType = "query"),
            @ApiImplicitParam(name = "stageId", value = "阶段id", required = false, paramType = "query")
    })
    @GetMapping("/mapBranchStage")
    public BaseResult<Map<Long, BranchStageDto>> mapBranchStage(@RequestParam List<Long> ids, @RequestParam(required = false) Long stageId) {
        Map<Long, BranchStageDto> list = featureService.mapBranchStage(ids, stageId);
        return BaseResult.ok(list);
    }

    @ApiOperation("查看特性详情")
    @GetMapping("/getById/{id}")
    public BaseResult query(@ApiParam("id") @PathVariable Long id) {
        return BaseResult.ok(featureService.getByIdWithGitlab(id));
    }


    @ApiOperation("查看特性详情")
    @GetMapping("/baseById")
    public BaseResult baseById(@ApiParam("id") @RequestParam Long id) {
        return BaseResult.ok(featureService.getById(id));
    }

    @ApiOperation("特性关联任务列表")
    @GetMapping("/task")
    public BaseResult<List<WorkItem>> task(@ApiParam("featureId") @RequestParam Long featureId) {
        return BaseResult.ok(featureService.task(featureId));
    }

    /**
     * 特性任务分页列表(过滤当前特性已关联的任务)
     *
     * @param req
     * @return
     */
    @ApiOperation("特性任务分页列表")
    @GetMapping("/taskPage")
    public BaseResult<Page<WorkItem>> taskPage(FeatureTaskQueryReq req) {
        return BaseResult.ok(featureService.taskPage(req));
    }

    @OperationAudit(operationName = "特性-特性关联任务",
            operationCode = "研发协同-特性"
    )
    @ApiOperation("添加关联任务")
    @PostMapping("/addTask")
    public BaseResult addTask(@RequestBody FeatureTaskAddReq req) {
        featureService.addTask(req);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "特性-移除特性任务",
            operationCode = "研发协同-特性"
    )
    @ApiOperation("移除特性任务")
    @PostMapping("/delTask")
    public BaseResult delTask(@RequestBody FeatureTaskDelReq req) {
        featureService.delTask(req);
        return BaseResult.ok();
    }

    @ApiOperation("查询特性可关联的工作项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "dimension", value = "查询维度：0-全部；1-当前登录人负责的", required = true, paramType = "query"),
            @ApiImplicitParam(name = "issuesClassicIdList", value = "事项类型：1-需求；2-任务；3-缺陷", required = true, paramType = "query")
    })
    @GetMapping("/taskList")
    public BaseResult<List<IssuesDto>> taskList(@RequestParam Long projectId,
                                                @RequestParam Integer dimension,
                                                @RequestParam List<Long> issuesClassicIdList) {
       //todo
        // List<IssuesDto> issuesDTOS = projectManagementService.taskList(projectId, dimension, issuesClassicIdList);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "创建功能分支：${featureName}",
            operationCode = "研发协同-功能分支",
            operationId = "${featureName}",
            dynOperationValues = {
                    @DynOperationValue(key = "featureName", jstl = "$[0].featureName")
            })
    @ApiOperation("创建特性")
    @PostMapping("/create")
    public BaseResult create(@Valid @RequestBody FeatureCreateRequest request) {
        DevopsFeature devopsFeature = featureService.create(request);
        FeatureBranch byFeatureId = featureBranchRepository.getByParam(devopsFeature.getId());
        Map<String, String> param = new HashMap<>();
        param.put("featureName", devopsFeature.getFeatureName());
        param.put("branchName", byFeatureId.getBranchName());
        String msg = "${featureName}创建成功，对应代码受控分支为${branchName}";
        if(BranchModelEnum.CONTROLLED.getCode().equals(devopsFeature.getBranchModel())){
            param.put("branchDevName", byFeatureId.getBranchDevName());
            msg += "， 开发分支为${branchDevName}";
        }
        StringSubstitutor ss = new StringSubstitutor(param);
        return BaseResult.ok(devopsFeature).setMsg(ss.replace(msg));
    }

    @OperationAudit(operationName = "更新功能分支：${featureName}",
            operationCode = "研发协同-功能分支",
            operationId = "${featureName}",
            dynOperationValues = {
                    @DynOperationValue(key = "featureName", jstl = "$[0].featureName")
            })
    @ApiOperation("更新功能分支")
    @PostMapping("/update")
    public BaseResult update(@Valid @RequestBody FeatureUpdateRequest request) {
        featureService.update(request);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "删除功能分支：${id}",
            operationCode = "研发协同-功能分支",
            operationId = "${id}",
            dynOperationValues = {
                    @DynOperationValue(key = "id", jstl = "$[0]")
            })
    @ApiOperation("删除功能分支")
    @PostMapping("/delete/{id}")
    public BaseResult update(@PathVariable Long id) {
        featureService.delete(id);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "清理功能分支",
            operationCode = "研发协同-功能分支"
            )
    @ApiOperation("清理功能分支")
    @PostMapping("/deleteBranch")
    public BaseResult deleteBranch(@RequestBody FeatureClearRequest request) {
        FeatureClearResponse response = featureService.deleteBranch(request);
        if(!response.getSuccess()){
            return BaseResult.failed(response).setCode(ExceptionCode.CLEAR_BRANCH_CHECK_MERGE.getCode()).setMsg(response.getNMerge().stream().collect(Collectors.joining(",")) + "  分支未合并，清理终止");
        }
        return BaseResult.ok(response);
    }

    @ApiOperation("查询功能分支关联需求")
    @GetMapping("/listIssues")
    public BaseResult<List<IssuesDTO>> listIssues(@RequestParam List<Long> ids){
      //todo
        // return BaseResult.ok(projectManagementService.getCustomIssuesByFeatureIds(ids));
      return BaseResult.ok();
    }
    @ApiOperation("创建分支")
    @PostMapping("/createFeature")
    public BaseResult createFeature(@RequestBody FeatureCreateRequestDto request){
        featureService.createFeature(request);
        return BaseResult.ok();
    }

}
