package cn.harmonycloud.development.inbound.provider;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.DevopsSystemRepository;
import cn.harmonycloud.development.pojo.dto.system.DevopsSystemDto;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.pojo.vo.system.ListSystemByCurrentRequest;
import cn.harmonycloud.development.pojo.vo.system.ListSystemByCurrentVo;
import cn.harmonycloud.development.pojo.vo.system.SubSystemGitlab;
import cn.harmonycloud.development.pojo.vo.system.SystemDataVO;
import cn.harmonycloud.development.service.SystemService;
import cn.harmonycloud.development.service.mapstruct.DevopsSystemMapstruct;
import cn.harmonycloud.pojo.system.SystemQuery;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/19 7:19 下午
 **/
@RestController
@RequestMapping("/provider/system")
public class SystemProvider {

    @Autowired
    private SystemService systemService;


    @Autowired
    private DevopsSystemRepository devopsSystemRepository;

    @Autowired
    private DevopsSystemMapstruct devopsSystemMapstruct;

    @ApiOperation("获取系统详情")
    @GetMapping("/getById")
    public BaseResult<SystemDataVO> getByPrimaryKey(@RequestParam long systemId) {
        DevopsSystem devopsSystem = devopsSystemRepository.getById(systemId);
        SystemDataVO systemDataVO = devopsSystemMapstruct.toSystemDataVO(devopsSystem);
        systemDataVO = systemService.getProjects(systemDataVO);
        return BaseResult.ok(systemDataVO);
    }

    @ApiOperation("系统列表")
    @GetMapping("/list")
    public BaseResult<List<DevopsSystemDto>> list(SystemQuery systemQuery) {
        List<DevopsSystemDto> list = systemService.listAll(systemQuery);
        return BaseResult.ok(list);
    }


}
