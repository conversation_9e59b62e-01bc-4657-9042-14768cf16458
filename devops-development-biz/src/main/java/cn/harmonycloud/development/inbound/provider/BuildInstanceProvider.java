package cn.harmonycloud.development.inbound.provider;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.BuildInstanceRepository;
import cn.harmonycloud.development.outbound.TestManagementRepository;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageDto;
import cn.harmonycloud.development.pojo.dto.feature.FeatureFeatureBranchDTO;
import cn.harmonycloud.development.pojo.entity.BuildInstance;
import cn.harmonycloud.development.pojo.entity.TestManagement;
import cn.harmonycloud.development.service.DevopsStageService;
import cn.harmonycloud.development.service.FeatureService;
import cn.harmonycloud.development.service.mapstruct.FeatureMapstruct;
import cn.harmonycloud.development.service.mapstruct.TestManagementMapstruct;
import cn.harmonycloud.pojo.BuildInstanceDto;
import cn.harmonycloud.pojo.FeatureInfoDto;
import cn.harmonycloud.pojo.TestManageDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/21 9:53 上午
 **/
@Api(tags = "构建实例信息")
@RequestMapping("/provider/buildInstance")
@RestController
public class BuildInstanceProvider {

    @Autowired
    private BuildInstanceRepository buildInstanceRepository;
    @Autowired
    private FeatureService featureService;
    @Autowired
    private DevopsStageService devopsStageService;
    @Autowired
    private TestManagementRepository testManagementRepository;
    @Autowired
    private FeatureMapstruct featureMapstruct;
    @Autowired
    private TestManagementMapstruct testManagementMapstruct;

    @ApiOperation("查询构建实例信息")
    @GetMapping("/info")
    public BaseResult<BuildInstanceDto> info(@RequestParam Long buildId) {
        BuildInstance buildInstance = buildInstanceRepository.getByBuildId(buildId);
        if(buildInstance == null ){
            return BaseResult.ok();
        }
        List<TestManagement> testManagements = testManagementRepository.listByParams(buildInstance.getId());
        BuildInstanceDto buildInstanceDto = new BuildInstanceDto();
        if(CollectionUtils.isNotEmpty(testManagements)){
            List<TestManageDto> collect = testManagements.stream().map(tm -> testManagementMapstruct.toTestManageDto(tm)).collect(Collectors.toList());
            buildInstanceDto.setTestManagements(collect);
        }
        if(CollectionUtils.isNotEmpty(buildInstance.getFeatureId())){
            List<FeatureFeatureBranchDTO> featureBranch = featureService.listFeatureBranch(buildInstance.getFeatureId(), false);
            DevopsStageDto devopsStageDto = devopsStageService.infoByStageEnvId(buildInstance.getStageEnvId());
            List<FeatureInfoDto> collect = featureBranch.stream().map(f -> {
                FeatureInfoDto featureInfoDto = featureMapstruct.toFeatureInfoDto(f);
                featureInfoDto.setBranch(f.getBranch(devopsStageDto.getType()));
                return featureInfoDto;
            }).collect(Collectors.toList());
            buildInstanceDto.setFeatures(collect);
        }

        buildInstanceDto.setVersionId(buildInstanceDto.getVersionId());
        buildInstanceDto.setBuildId(buildInstanceDto.getBuildId());
        return BaseResult.ok(buildInstanceDto);
    }

}
