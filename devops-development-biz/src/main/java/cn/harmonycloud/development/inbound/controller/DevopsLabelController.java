package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.DevopsLabelRepository;
import cn.harmonycloud.development.pojo.vo.label.DevopsLabelCreateDto;
import cn.harmonycloud.development.pojo.vo.label.DevopsLabelRemoveDto;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/19 5:25 下午
 **/
@Validated
@Api(tags = "标签管理")
@RequestMapping("/devopsLabel")
@RestController
public class DevopsLabelController {

    @Autowired
    private DevopsLabelRepository devopsLabelRepository;

    @OperationAudit(operationName = "标签管理-新增标签绑定",
            operationCode = "研发协同-标签"
    )
    @ApiOperation("添加标签")
    @PostMapping("/create")
    public BaseResult create(@Valid @RequestBody DevopsLabelCreateDto request) {
        devopsLabelRepository.removeAndSave(request);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "标签管理-删除标签绑定",
            operationCode = "研发协同-标签"
    )
    @ApiOperation("取消标签")
    @PostMapping("/remove")
    public BaseResult remove(@Valid @RequestBody DevopsLabelRemoveDto request) {
        devopsLabelRepository.removeByParams(request.getClassificationCode(), request.getInstanceId(), request.getLabelId());
        return BaseResult.ok();
    }
}
