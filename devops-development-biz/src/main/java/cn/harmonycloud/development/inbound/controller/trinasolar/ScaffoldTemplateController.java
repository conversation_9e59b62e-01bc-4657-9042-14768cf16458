package cn.harmonycloud.development.inbound.controller.trinasolar;


import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.vo.thgn.scaffold.ScaffoldTemplateVO;
import cn.harmonycloud.development.service.trinasolar.ScaffoldTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 脚手架管理
 * <AUTHOR> Gong
 * @Date 2025/3/25
 **/
@Api(tags = "脚手架管理")
@RequestMapping("/scaffold/template")
@RestController
public class ScaffoldTemplateController {

    @Autowired
    private ScaffoldTemplateService scaffoldTemplateService;

    @ApiOperation("脚手架展示")
    @PostMapping("/list")
    public BaseResult<List<ScaffoldTemplateVO>> list(@RequestBody List<String> techStacks) {
        return BaseResult.ok(scaffoldTemplateService.list(techStacks));
    }











}
