package cn.harmonycloud.development.outbound.util;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/12 5:10 下午
 **/
public class WrapperUtil {

    public static String ESCAPE_CHAR = "^";

    public static String escapeWhereLike(String str){
        return  "%" + escapeValue(str) + "%";
    }

    public static String escapeValue(String str){
        String escapeStr = str;
        if(str != null && str.length() > 0){
            escapeStr= str.replaceAll("_", ESCAPE_CHAR + "_");
            escapeStr= escapeStr.replaceAll("%", ESCAPE_CHAR + "%");
        }
        return  escapeStr;
    }

    public static boolean isEscape(String str){
        return str != null && str.length() > 0 && (str.contains("_") || str.contains("%"));
    }


}
