package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.VersionManagement;
import cn.harmonycloud.development.pojo.entity.VersionSubsystem;
import cn.harmonycloud.development.pojo.vo.version.VersionQuery;
import cn.harmonycloud.development.pojo.vo.version.VersionSubsystemQuery;
import cn.harmonycloud.mybatis.base.BaseRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface VersionRepository extends BaseRepository<VersionManagement> {

    /**
     * 逻辑删除版本信息
     *
     * @param versionId
     */
    void deleteLogic(Long versionId);

    /**
     * 根据版本号获取最近一次版本
     *
     * @param versionType
     * @param versionNumber
     * @return
     */
    VersionManagement getLastVersion(Long subsystemId, Integer versionType, String versionNumber);

    /**
     * 根据参数获取版本列表
     *
     * @param subSystemId
     * @param versionNumber
     * @param versionNumber
     * @return
     */
    List<VersionManagement> listByParams(Long subSystemId, String versionNumber, @Nullable String patchNumber);

    /**
     * 根据参数获取版本列表
     *
     * @param subSystemId
     * @param versionNumber
     * @param switchStatus 是否开启
     * @return
     */
    List<VersionManagement> listByStatus(Long subSystemId, String versionNumber, @Nullable Integer switchStatus);

    /**
     * 根据参数获取版本列表
     *
     * @param subSystemId
     * @return
     */
    List<VersionManagement> listByParams(Long subSystemId, Integer limit);

    /**
     * 分页查询版本
     *
     * @param request
     * @return
     */
    Page<VersionManagement> pageQuery(VersionQuery request);

    /**
     * 最近一次版本
     *
     * @param subsystemId （子系统id）
     * @return
     */
    VersionManagement last(Long subsystemId);

    List<VersionManagement> listByParams(List<Long> ids, List<Long> subsystemIds);

    /**
     * 更据版本号查询版本
     *
     * @param version 模糊查询
     * @return
     */
    List<VersionManagement> versionList(String version);


    List<VersionManagement> listByMajorVersionId(Long majorVersionId);


    VersionManagement getOpened(Long majorVersionId);

    void closeAll(Long majorVersionId , Long updateId , LocalDateTime now);

    VersionManagement getByParams(String totalVersionNumber, Long subsystemId);

    /**
     * 查询子版本列表
     *
     * @param majorVersionIds
     */
    List<VersionManagement> listByMajorVersionIds(List<Long> majorVersionIds,  Integer switchStatus);

    /**
     * 逻辑删除子版本
     *
     * @param majorVersionId
     * @return
     */
    boolean deleteLogicByMajorId(Long majorVersionId);

    /**
     * 大版本id
     *
     * @param majorIds
     * @return
     */
    Map<Long, VersionManagement> mapOpened(List<Long> majorIds);
}
