package cn.harmonycloud.development.outbound.api.dto.scm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName ScanIssueVO
 * @Description
 * <AUTHOR>
 * @Date 2023/2/23 2:04 PM
 **/
@Data
@ApiModel(value = "ScanIssueVO", description = "扫描问题视图对象")
public class ScanIssueVO {

    @ApiModelProperty("id")
    private Long id;
    /**
     * 扫描历史id
     */
    @ApiModelProperty("扫描历史id")
    private Long historyId;

    /**
     * 问题文件id
     */
    @ApiModelProperty("问题文件id")
    private Long fileId;
    /**
     * 问题文件标识
     */
    @ApiModelProperty("问题文件标识")
    private String component;

    /**
     * 问题key
     */
    @ApiModelProperty("问题key")
    private String issueKey;

    /**
     * 问题类型
     *
     */
    @ApiModelProperty("问题类型")
    private String issueType;

    /**
     * 扫描类型
     */
    @ApiModelProperty("扫描任务类型")
    private String scanType;

    /**
     * 问题严重程度
     *
     */
    @ApiModelProperty("问题严重程度")
    private String severity;

    /**
     * 问题严重程度英文
     */
    @ApiModelProperty("问题严重程度英文")
    private String severityEng;

    /**
     * 问题状态
     *
     */
    @ApiModelProperty("问题状态")
    private String status;

    /**
     * 问题定位坐标
     */
    @ApiModelProperty("问题定位坐标")
    private RangeVO range;

    /**
     * 所匹配的规则
     */
    @ApiModelProperty("所匹配的规则")
    private String rule;

    /**
     * 问题提示信息
     */
    @ApiModelProperty("问题提示信息")
    private String message;

    private String hash;
    @ApiModelProperty("问题创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("0-未忽略，1-已忽略")
    private Integer ignore;

    @ApiModelProperty("忽略原因")
    private String ignoreMessage;

    @ApiModel(value = "RangeVO", description = "定位坐标视图对象")
    @Data
    public static class RangeVO {
        private int endLine;
        private int endOffset;
        private int startLine;
        private int startOffset;
    }

}
