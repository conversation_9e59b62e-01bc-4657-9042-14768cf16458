package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.pojo.entity.thgn.ScaffoldComponent;
import cn.harmonycloud.enums.ExceptionCode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GitFileUtil {

    /**
     * 处理后端代码，替换包名和项目名称
     *
     * @param sourcePath
     * @param targetPath
     * @param programNameEn
     * @param targetPackage
     * @param scaffoldName
     * @param packagePath
     * @throws IOException
     */
    public static void replaceAppNameAndPackage(Path sourcePath, Path targetPath, String programNameEn,
                                                String targetPackage, String scaffoldName, String packagePath) throws IOException {
        // 源包名和目标包名
        String sourcePackage = packagePath;

        // 源包路径和目标包路径（将点号替换为路径分隔符）
        String sourcePackagePath = sourcePackage.replace('.', File.separatorChar);
        String targetPackagePath = targetPackage.replace('.', File.separatorChar);

        // 需要处理的后端配置文件
        List<String> backendConfigFiles = Arrays.asList(
                "pom.xml", "application.yml", "application-dev.yml", ".gitignore"
        );

        // 需要处理的Java文件扩展名
        List<String> javaExtensions = Arrays.asList(".java");

        // 用于存储需要重命名的目录映射
        Map<Path, Path> directoriesToRename = new HashMap<>();

        // 遍历源目录中的所有文件
        Files.walk(sourcePath)
                .filter(path -> !isGitDirectory(path))
                .forEach(source -> {
                    try {
                        // 获取相对路径
                        Path relativePath = sourcePath.relativize(source);
                        String pathStr = relativePath.toString();

                        // 替换项目名称
                        if (pathStr.contains(scaffoldName)) {
                            pathStr = pathStr.replace(scaffoldName, programNameEn);
                        }

                        // 替换包路径
                        if (pathStr.contains(sourcePackagePath)) {
                            pathStr = pathStr.replace(sourcePackagePath, targetPackagePath);
                        }

                        // 创建新的相对路径
                        Path newRelativePath = Paths.get(pathStr);
                        Path dest = targetPath.resolve(newRelativePath);

                        if (Files.isDirectory(source)) {
                            // 创建目标目录
                            if (!Files.exists(dest)) {
                                Files.createDirectories(dest);
                            }

                            // 如果目录名包含项目名称，记录下来以便后续重命名
                            if (source.getFileName().toString().equals(scaffoldName)) {
                                Path newDest = dest.getParent().resolve(programNameEn);
                                directoriesToRename.put(dest, newDest);
                            }
                        } else {
                            // 确保父目录存在
                            Files.createDirectories(dest.getParent());

                            // 获取文件名和扩展名
                            String fileName = source.getFileName().toString();
                            String fileExtension = getFileExtension(fileName);

                            // 判断是否需要处理内容
                            boolean isConfigFile = backendConfigFiles.contains(fileName);
                            boolean isJavaFile = javaExtensions.contains(fileExtension);

                            if (isConfigFile || isJavaFile) {
                                // 读取文件内容
                                String content = new String(Files.readAllBytes(source), StandardCharsets.UTF_8);
                                boolean contentChanged = false;

                                // 替换包名
                                if (content.contains(sourcePackage)) {
                                    content = content.replace(sourcePackage, targetPackage);
                                    contentChanged = true;
                                }

                                // 替换项目名称
                                if (content.contains(scaffoldName)) {
                                    content = content.replace(scaffoldName, programNameEn);
                                    contentChanged = true;
                                }

                                if (contentChanged) {
                                    // 写入修改后的内容
                                    Files.write(dest, content.getBytes(StandardCharsets.UTF_8));
                                    log.info("已替换文件 {} 中的内容", newRelativePath);
                                } else {
                                    // 无需替换，直接复制
                                    Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                                }
                            } else {
                                // 非需要处理的文件，直接复制
                                Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                            }
                        }
                    } catch (IOException e) {
                        log.error("处理后端代码失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "处理后端代码失败: " + e.getMessage());
                    }
                });

        // 重命名收集到的目录
        renameDirectories(directoriesToRename);
    }

    /**
     * 重命名收集到的目录
     */
    public static void renameDirectories(Map<Path, Path> directoriesToRename) {
        // 从深层次目录开始，避免父目录重命名后找不到子目录
        directoriesToRename.entrySet().stream()
                .sorted((e1, e2) -> e2.getKey().toString().length() - e1.getKey().toString().length())
                .forEach(entry -> {
                    try {
                        Path oldPath = entry.getKey();
                        Path newPath = entry.getValue();

                        if (Files.exists(oldPath) && !Files.exists(newPath)) {
                            Files.move(oldPath, newPath);
                            log.info("重命名目录: {} -> {}", oldPath, newPath);
                        }
                    } catch (IOException e) {
                        log.error("重命名目录失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "重命名目录失败: " + e.getMessage());
                    }
                });
    }


    /**
     * 获取文件扩展名（包含点号）
     */
    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }

    /**
     * 判断路径是否是 Git 目录或其子目录
     * 只过滤 .git 目录本身，保留 .gitignore 等文件
     */
    public static boolean isGitDirectory(Path path) {
        Path fileName = path.getFileName();
        if (fileName == null) {
            return false;
        }

        // 检查当前路径是否是 .git 目录
        if (".git".equals(fileName.toString())) {
            log.debug("过滤 .git 目录: {}", path);
            return true;
        }

        // 检查路径中是否包含 .git 目录（但不是 .gitignore 等文件）
        Path current = path;
        while (current != null) {
            Path currentFileName = current.getFileName();
            if (currentFileName != null && ".git".equals(currentFileName.toString())) {
                log.debug("过滤 .git 子目录: {}", path);
                return true;
            }
            current = current.getParent();
        }

        // 特别记录 .gitignore 文件的处理
        if (".gitignore".equals(fileName.toString())) {
            log.info("保留 .gitignore 文件: {}", path);
        }

        return false;
    }

    /**
     * 简单复制文件，不执行替换和重命名
     */
    public static void simpleCopyFiles(Path sourcePath, Path targetPath) throws IOException {
        Files.walk(sourcePath)
                .filter(path -> !isGitDirectory(path))
                .forEach(source -> {
                    try {
                        Path relativePath = sourcePath.relativize(source);
                        Path dest = targetPath.resolve(relativePath);

                        if (Files.isDirectory(source)) {
                            if (!Files.exists(dest)) {
                                Files.createDirectories(dest);
                            }
                        } else {
                            Files.createDirectories(dest.getParent());
                            Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                        }
                    } catch (IOException e) {
                        log.error("复制文件失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "复制文件失败: " + e.getMessage());
                    }
                });
    }

    /**
     * 给pom 添加dependence依赖
     *
     * @param targetPath
     * @param scaffoldComponents
     */
    /**
     * 添加POM依赖组件到pom.xml文件
     * 在现有dependencies基础上新增dependency依赖项
     *
     * 功能说明：
     * 1. 读取现有的pom.xml文件，保持原有依赖不变
     * 2. 在</dependencies>标签前插入新的dependency依赖
     * 3. 自动格式化和缩进，确保XML格式正确
     * 4. 支持完整的<dependency>块或仅dependency内容
     * 5. 过滤空值和无效依赖
     *
     * 依赖格式支持：
     * - 完整格式：<dependency><groupId>...</groupId>...</dependency>
     * - 简化格式：<groupId>...</groupId><artifactId>...</artifactId>...
     *
     * @param targetPath         目标路径
     * @param scaffoldComponents 要添加的组件依赖列表
     * @throws GitException 当pom.xml格式错误或文件操作失败时抛出
     */
    public static void addPomStarterComponent(Path targetPath, List<ScaffoldComponent> scaffoldComponents) {
        List<String> depContentList = scaffoldComponents.stream().map(ScaffoldComponent::getDepContent).collect(Collectors.toList());

        File pomFile = targetPath.resolve("pom.xml").toFile();

        try {
            String pomContent = new String(Files.readAllBytes(pomFile.toPath()), StandardCharsets.UTF_8);

            // 查找dependencies结束标签的位置
            String dependenciesEnd = "</dependencies>";
            int endIndex = pomContent.indexOf(dependenciesEnd);

            if (endIndex == -1) {
                log.error("pom.xml中未找到</dependencies>标签");
                throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "pom.xml格式错误：缺少</dependencies>标签");
            }

            // 构建新的依赖内容（带格式化）
            StringBuilder newDependencies = new StringBuilder();
            for (String component : depContentList) {
                if (component != null && !component.trim().isEmpty()) {
                    // 格式化dependency，确保正确的缩进
                    String formattedDependency = formatDependency(component.trim());
                    newDependencies.append(formattedDependency).append("\n");
                }
            }

            // 在</dependencies>标签前插入新的依赖
            String updatedPomContent = pomContent.substring(0, endIndex) +
                    newDependencies.toString() +
                    "    " + pomContent.substring(endIndex); // 为</dependencies>标签添加适当缩进

            // 将修改后的pom.xml写入到targetPath目录
            Path targetPomPath = targetPath.resolve("pom.xml");
            Files.write(targetPomPath, updatedPomContent.getBytes(StandardCharsets.UTF_8));

            log.info("成功添加 {} 个组件依赖到 pom.xml，并保存到: {}", scaffoldComponents.size(), targetPomPath);

        } catch (IOException e) {
            log.error("修改pom文件失败: {}", e.getMessage());
            throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "修改pom文件失败: " + e.getMessage());
        }
    }

    /**
     * 格式化dependency依赖，确保正确的缩进
     *
     * @param dependency 依赖内容
     * @return 格式化后的依赖字符串
     */
    private static String formatDependency(String dependency) {
        // 如果dependency已经包含<dependency>标签，直接格式化缩进
        if (dependency.contains("<dependency>")) {
            return indentDependency(dependency);
        }

        // 如果只是dependency内容，需要包装在<dependency>标签中
        return "        <dependency>\n" +
               indentContent(dependency) +
               "        </dependency>";
    }

    /**
     * 为dependency添加适当的缩进
     */
    private static String indentDependency(String dependency) {
        String[] lines = dependency.split("\n");
        StringBuilder result = new StringBuilder();

        for (String line : lines) {
            if (!line.trim().isEmpty()) {
                // 确保每行都有适当的缩进（8个空格）
                if (line.startsWith("        ")) {
                    result.append(line).append("\n");
                } else if (line.trim().startsWith("<")) {
                    result.append("        ").append(line.trim()).append("\n");
                } else {
                    result.append("            ").append(line.trim()).append("\n");
                }
            }
        }

        return result.toString();
    }

    /**
     * 为dependency内容添加缩进
     */
    private static String indentContent(String content) {
        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();

        for (String line : lines) {
            if (!line.trim().isEmpty()) {
                result.append("            ").append(line.trim()).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 添加应用配置到 application.yml 文件
     *
     * @param targetPath 目标路径
     * @param scaffoldComponents 脚手架组件列表，包含要合并的配置内容
     * @throws IOException 文件操作异常
     */
    public static void addApplicationConfig(Path targetPath, List<ScaffoldComponent> scaffoldComponents) throws IOException {
        ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());

        File applicationFile = targetPath.resolve("src/main/resources/application.yml").toFile();
        JsonNode applicationConfig = yamlMapper.readTree(applicationFile);

        // 提取要新增的配置
        List<Map<String, String>> configContentList = scaffoldComponents
                .stream()
                .map(ScaffoldComponent::getConfigContent)
                .filter(Objects::nonNull) // 过滤空配置
                .collect(Collectors.toList());

        log.info("开始合并 {} 个组件的配置到 application.yml", configContentList.size());

        for (Map<String, String> configContent : configContentList) {
            try {
                String contentStr = configContent.get("content");
                if (contentStr != null && !contentStr.trim().isEmpty()) {
                    JsonNode content = yamlMapper.readTree(contentStr);
                    if (content != null && !content.isNull()) {
                        applicationConfig = mergeYamlNodes(applicationConfig, content);
                        log.debug("成功合并配置: {}", contentStr);
                    }
                }
            } catch (JsonProcessingException e) {
                log.error("解析配置内容失败: {}", configContent.get("content"), e);
                throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "解析YAML配置失败: " + e.getMessage());
            }
        }

        // 确保目标目录存在
        Files.createDirectories(targetPath);

        // 将合并后的配置写回文件
        Path targetApplicationPath = targetPath.resolve("application.yml");
        yamlMapper.writeValue(targetApplicationPath.toFile(), applicationConfig);

        log.info("成功合并配置并保存到: {}", targetApplicationPath);
    }

    /**
     * 合并两个YAML节点
     *
     * 合并规则：
     * 1. 对象节点：递归合并，content中的值会覆盖applicationConfig中的同名字段
     * 2. 数组节点：content中的数组会替换applicationConfig中的数组
     * 3. 基本类型：content中的值会覆盖applicationConfig中的值
     * 4. null值：如果content为null，保持applicationConfig的值不变
     *
     * @param applicationConfig 原始配置节点
     * @param content 要合并的新配置节点
     * @return 合并后的配置节点
     */
    private static JsonNode mergeYamlNodes(JsonNode applicationConfig, JsonNode content) {
        if (content == null || content.isNull()) {
            return applicationConfig;
        }

        if (applicationConfig == null || applicationConfig.isNull()) {
            return content;
        }

        // 如果两个节点都是对象类型，进行深度合并
        if (applicationConfig.isObject() && content.isObject()) {
            return mergeObjectNodes((ObjectNode) applicationConfig, (ObjectNode) content);
        }

        // 如果两个节点都是数组类型，合并数组
        if (applicationConfig.isArray() && content.isArray()) {
            return mergeArrayNodes((ArrayNode) applicationConfig, (ArrayNode) content);
        }

        // 其他情况，content覆盖applicationConfig
        return content;
    }

    /**
     * 合并两个对象节点
     */
    private static ObjectNode mergeObjectNodes(ObjectNode target, ObjectNode source) {
        ObjectNode result = target.deepCopy();

        Iterator<Map.Entry<String, JsonNode>> fields = source.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String fieldName = entry.getKey();
            JsonNode sourceValue = entry.getValue();
            JsonNode targetValue = result.get(fieldName);

            if (targetValue != null && targetValue.isObject() && sourceValue.isObject()) {
                // 递归合并对象
                JsonNode mergedValue = mergeObjectNodes((ObjectNode) targetValue, (ObjectNode) sourceValue);
                result.set(fieldName, mergedValue);
            } else if (targetValue != null && targetValue.isArray() && sourceValue.isArray()) {
                // 合并数组
                JsonNode mergedValue = mergeArrayNodes((ArrayNode) targetValue, (ArrayNode) sourceValue);
                result.set(fieldName, mergedValue);
            } else {
                // 直接覆盖或添加新字段
                result.set(fieldName, sourceValue);
            }
        }

        return result;
    }

    /**
     * 合并两个数组节点
     * 策略：将source数组的元素添加到target数组中，避免重复
     */
    private static ArrayNode mergeArrayNodes(ArrayNode target, ArrayNode source) {
        ArrayNode result = target.deepCopy();

        for (JsonNode sourceElement : source) {
            // 检查是否已存在相同元素（避免重复）
            boolean exists = false;
            for (JsonNode targetElement : result) {
                if (targetElement.equals(sourceElement)) {
                    exists = true;
                    break;
                }
            }

            if (!exists) {
                result.add(sourceElement);
            }
        }

        return result;
    }

    /**
     * 测试YAML合并功能的示例方法
     *
     * 使用示例：
     * 原始配置：
     * server:
     *   port: 8080
     * spring:
     *   datasource:
     *     url: ********************************
     *
     * 新增配置：
     * server:
     *   servlet:
     *     context-path: /api
     * spring:
     *   datasource:
     *     username: root
     *     password: 123456
     *   redis:
     *     host: localhost
     *     port: 6379
     *
     * 合并结果：
     * server:
     *   port: 8080
     *   servlet:
     *     context-path: /api
     * spring:
     *   datasource:
     *     url: ********************************
     *     username: root
     *     password: 123456
     *   redis:
     *     host: localhost
     *     port: 6379
     */
//    public static void testYamlMerge() {
//        try {
//            ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());
//
//            String originalYaml = """
//                server:
//                  port: 8080
//                spring:
//                  datasource:
//                    url: ********************************
//                """;
//
//            String newYaml = """
//                server:
//                  servlet:
//                    context-path: /api
//                spring:
//                  datasource:
//                    username: root
//                    password: 123456
//                  redis:
//                    host: localhost
//                    port: 6379
//                """;
//
//            JsonNode original = yamlMapper.readTree(originalYaml);
//            JsonNode newConfig = yamlMapper.readTree(newYaml);
//            JsonNode merged = mergeYamlNodes(original, newConfig);
//
//            String result = yamlMapper.writeValueAsString(merged);
//            log.info("YAML合并测试结果:\n{}", result);
//
//        } catch (Exception e) {
//            log.error("YAML合并测试失败", e);
//        }
//    }
}
