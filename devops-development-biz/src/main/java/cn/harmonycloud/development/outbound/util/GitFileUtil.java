package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.enums.ExceptionCode;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class GitFileUtil {

    /**
     * 处理后端代码，替换包名和项目名称
     *
     * @param sourcePath
     * @param targetPath
     * @param programNameEn
     * @param targetPackage
     * @param scaffoldName
     * @param packagePath
     * @throws IOException
     */
    public static void replaceAppNameAndPackage(Path sourcePath, Path targetPath, String programNameEn,
                                                String targetPackage, String scaffoldName, String packagePath) throws IOException {
        // 源包名和目标包名
        String sourcePackage = packagePath;

        // 源包路径和目标包路径（将点号替换为路径分隔符）
        String sourcePackagePath = sourcePackage.replace('.', File.separatorChar);
        String targetPackagePath = targetPackage.replace('.', File.separatorChar);

        // 需要处理的后端配置文件
        List<String> backendConfigFiles = Arrays.asList(
                "pom.xml", "application.yml", "application-dev.yml", ".gitignore"
        );

        // 需要处理的Java文件扩展名
        List<String> javaExtensions = Arrays.asList(".java");

        // 用于存储需要重命名的目录映射
        Map<Path, Path> directoriesToRename = new HashMap<>();

        // 遍历源目录中的所有文件
        Files.walk(sourcePath)
                .filter(path -> !isGitDirectory(path))
                .forEach(source -> {
                    try {
                        // 获取相对路径
                        Path relativePath = sourcePath.relativize(source);
                        String pathStr = relativePath.toString();

                        // 替换项目名称
                        if (pathStr.contains(scaffoldName)) {
                            pathStr = pathStr.replace(scaffoldName, programNameEn);
                        }

                        // 替换包路径
                        if (pathStr.contains(sourcePackagePath)) {
                            pathStr = pathStr.replace(sourcePackagePath, targetPackagePath);
                        }

                        // 创建新的相对路径
                        Path newRelativePath = Paths.get(pathStr);
                        Path dest = targetPath.resolve(newRelativePath);

                        if (Files.isDirectory(source)) {
                            // 创建目标目录
                            if (!Files.exists(dest)) {
                                Files.createDirectories(dest);
                            }

                            // 如果目录名包含项目名称，记录下来以便后续重命名
                            if (source.getFileName().toString().equals(scaffoldName)) {
                                Path newDest = dest.getParent().resolve(programNameEn);
                                directoriesToRename.put(dest, newDest);
                            }
                        } else {
                            // 确保父目录存在
                            Files.createDirectories(dest.getParent());

                            // 获取文件名和扩展名
                            String fileName = source.getFileName().toString();
                            String fileExtension = getFileExtension(fileName);

                            // 判断是否需要处理内容
                            boolean isConfigFile = backendConfigFiles.contains(fileName);
                            boolean isJavaFile = javaExtensions.contains(fileExtension);

                            if (isConfigFile || isJavaFile) {
                                // 读取文件内容
                                String content = new String(Files.readAllBytes(source), StandardCharsets.UTF_8);
                                boolean contentChanged = false;

                                // 替换包名
                                if (content.contains(sourcePackage)) {
                                    content = content.replace(sourcePackage, targetPackage);
                                    contentChanged = true;
                                }

                                // 替换项目名称
                                if (content.contains(scaffoldName)) {
                                    content = content.replace(scaffoldName, programNameEn);
                                    contentChanged = true;
                                }

                                if (contentChanged) {
                                    // 写入修改后的内容
                                    Files.write(dest, content.getBytes(StandardCharsets.UTF_8));
                                    log.info("已替换文件 {} 中的内容", newRelativePath);
                                } else {
                                    // 无需替换，直接复制
                                    Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                                }
                            } else {
                                // 非需要处理的文件，直接复制
                                Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                            }
                        }
                    } catch (IOException e) {
                        log.error("处理后端代码失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "处理后端代码失败: " + e.getMessage());
                    }
                });

        // 重命名收集到的目录
        renameDirectories(directoriesToRename);
    }

    /**
     * 重命名收集到的目录
     */
    public static void renameDirectories(Map<Path, Path> directoriesToRename) {
        // 从深层次目录开始，避免父目录重命名后找不到子目录
        directoriesToRename.entrySet().stream()
                .sorted((e1, e2) -> e2.getKey().toString().length() - e1.getKey().toString().length())
                .forEach(entry -> {
                    try {
                        Path oldPath = entry.getKey();
                        Path newPath = entry.getValue();

                        if (Files.exists(oldPath) && !Files.exists(newPath)) {
                            Files.move(oldPath, newPath);
                            log.info("重命名目录: {} -> {}", oldPath, newPath);
                        }
                    } catch (IOException e) {
                        log.error("重命名目录失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "重命名目录失败: " + e.getMessage());
                    }
                });
    }


    /**
     * 获取文件扩展名（包含点号）
     */
    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }

    /**
     * 判断路径是否是 Git 目录或其子目录
     * 只过滤 .git 目录本身，保留 .gitignore 等文件
     */
    public static boolean isGitDirectory(Path path) {
        Path fileName = path.getFileName();
        if (fileName == null) {
            return false;
        }

        // 检查当前路径是否是 .git 目录
        if (".git".equals(fileName.toString())) {
            log.debug("过滤 .git 目录: {}", path);
            return true;
        }

        // 检查路径中是否包含 .git 目录（但不是 .gitignore 等文件）
        Path current = path;
        while (current != null) {
            Path currentFileName = current.getFileName();
            if (currentFileName != null && ".git".equals(currentFileName.toString())) {
                log.debug("过滤 .git 子目录: {}", path);
                return true;
            }
            current = current.getParent();
        }

        // 特别记录 .gitignore 文件的处理
        if (".gitignore".equals(fileName.toString())) {
            log.info("保留 .gitignore 文件: {}", path);
        }

        return false;
    }

    /**
     * 简单复制文件，不执行替换和重命名
     */
    public static void simpleCopyFiles(Path sourcePath, Path targetPath) throws IOException {
        Files.walk(sourcePath)
                .filter(path -> !isGitDirectory(path))
                .forEach(source -> {
                    try {
                        Path relativePath = sourcePath.relativize(source);
                        Path dest = targetPath.resolve(relativePath);

                        if (Files.isDirectory(source)) {
                            if (!Files.exists(dest)) {
                                Files.createDirectories(dest);
                            }
                        } else {
                            Files.createDirectories(dest.getParent());
                            Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                        }
                    } catch (IOException e) {
                        log.error("复制文件失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "复制文件失败: " + e.getMessage());
                    }
                });
    }

    /**
     * 给pom 添加dependence依赖
     *
     * @param targetPath
     * @param scaffoldComponents
     */
    public static void addPomStarterComponent(Path targetPath, List<String> scaffoldComponents) {
        File pomFile = targetPath.resolve("pom.xml").toFile();

        
    }
}
