//package cn.harmonycloud.hzbank.outbound.db.mapper;
//
//import cn.harmonycloud.hzbank.pojo.entity.SystemStar;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.Date;
//
///**
//* <AUTHOR>
//* @description 针对表【system_star(系统表)】的数据库操作Mapper
//* @createDate 2022-08-02 10:14:00
//* @Entity cn.harmonycloud.hzbank.pojo.entity.SystemStar
//*/
//public interface SystemStarMapper extends BaseMapper<SystemStar> {
//    void insertDuplicate(@Param("userId") Long userId, @Param("systemId") Long systemId,
//                         @Param("type") String type, @Param("data") Date date);
//}
//
//
//
//
