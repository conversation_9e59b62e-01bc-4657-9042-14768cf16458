package cn.harmonycloud.development.outbound.api.dto.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/10 5:09 下午
 **/
@Data
@ApiModel("配置库创建")
public class ConfigCreateDTO {

    @ApiModelProperty("配置库名称")
    private String projectName;

    @ApiModelProperty("配置库描述")
    private String description;

    public ConfigCreateDTO(String projectName, String description) {
        this.projectName = projectName;
        this.description = description;
    }

}
