package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.DevopsStageRepository;
import cn.harmonycloud.development.outbound.db.mapper.DevopsStageMapper;
import cn.harmonycloud.development.pojo.entity.DevopsStage;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/6 4:38 下午
 **/
@Service
public class DevopsStageRepositoryImpl extends BaseRepositoryImpl<DevopsStageMapper, DevopsStage> implements DevopsStageRepository {


    @Override
    public List<DevopsStage> listBySubsystemId(Long subsystemId) {
        LambdaQueryWrapper<DevopsStage> query = new LambdaQueryWrapper<>();
        query.eq(DevopsStage::getSubsystemId, subsystemId);
        query.orderByAsc(DevopsStage::getSort);
        Optional<List<DevopsStage>> list = Optional.ofNullable(list(query));
        return list.orElse(new ArrayList<>());
    }

    @Override
    public void updateEnvId(List<Long> subsystemIds, Integer envId, Integer newEnvId) {
        if (envId == null || newEnvId == null){
            return;
        }
        if (envId == newEnvId){
            return;
        }
        LambdaUpdateWrapper<DevopsStage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(DevopsStage::getSubsystemId, subsystemIds);
        updateWrapper.eq(DevopsStage::getEnvId, envId);
        updateWrapper.set(DevopsStage::getEnvId, newEnvId);
        this.update(updateWrapper);
    }

}
