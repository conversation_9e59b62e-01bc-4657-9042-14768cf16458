package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.JobRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.PipelineBindingDto;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.JobFeign;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.outbound.api.dto.pipeline.*;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.mybatis.base.BaseQuery;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.tenant.TenantContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/11 9:14 上午
 **/
@Service
public class JobRepositoryImpl implements JobRepository, ApiRepository {

    @Autowired
    private JobFeign jobFeign;


    @Override
    public void bindingSubsystem(Long systemId, Long subsystemId, Map<Long, List<Long>> userMap) {
        if (subsystemId == null || userMap == null || userMap.keySet().size() == 0) {
            return;
        }
        PipelineBindingDto pipelineBindingDto = new PipelineBindingDto();
        pipelineBindingDto.setSystemId(systemId);
        pipelineBindingDto.setSubSystemId(subsystemId);
        pipelineBindingDto.setUserMap(userMap);
        feignExecute(() -> jobFeign.binding(pipelineBindingDto));
    }

    @Override
    public void bindingSubsystem(Long systemId, Long subsystemId, List<Long> jobIds) {
        if (subsystemId == null || jobIds == null || jobIds.size() == 0) {
            return;
        }
        PipelineBindingDto pipelineBindingDto = new PipelineBindingDto();
        pipelineBindingDto.setSystemId(systemId);
        pipelineBindingDto.setSubSystemId(subsystemId);
        pipelineBindingDto.setResourceInstanceIds(jobIds);
        feignExecute(() -> jobFeign.binding(pipelineBindingDto));
    }

    @Override
    public List<JobDto> listJob(List<Long> jobIds, Boolean resourceFlag, String draftStatus) {
        if (CollectionUtils.isEmpty(jobIds)) {
            return new ArrayList<>();
        }
        List<String> collect = jobIds.stream().map(id -> String.valueOf(id)).collect(Collectors.toList());
        return feignExecute(() -> jobFeign.listByIds(collect, resourceFlag, draftStatus));
    }

    @Override
    public BuildDetailDto getRecentBuildByJobId(Long jobId) {
        return feignExecute(() -> jobFeign.getRecentBuildByJobId(jobId));
    }

    @Override
    public BuildDetailDto getRecentBuildByBuildId(Long jobId, Long buildId) {
        return feignExecute(() -> jobFeign.getRecentBuildDetail(jobId, buildId));
    }

    @Override
    public Long buildJob(List<JenkinsFileStartParameter> startParam, Long jobId, User currenUser, String runDescribe) {
        BuildDto buildDTO = new BuildDto();
        buildDTO.setUserId(currenUser.getId() + "");
        buildDTO.setUserName(currenUser.getUsername());
        buildDTO.setStartParams(startParam);
        buildDTO.setJobId(String.valueOf(jobId));
        buildDTO.setRunDescribe(runDescribe);
        return feignExecute(() -> jobFeign.buildJob(buildDTO));
    }

    @Override
    public Long createJob(DevopsSubSystem subSystem, DevopsSystem system, List<StartParamDto> start, List<EnvParamDto> env, Long templateId) {
        PipelineJobDto pipelineJobDto = new PipelineJobDto();
        pipelineJobDto.setTenant(TenantContextHolder.getTenantId());
        pipelineJobDto.setSubSystemId(subSystem.getId());
        pipelineJobDto.setSystemId(system.getId());
        pipelineJobDto.setEnvParam(env);
        pipelineJobDto.setStartParam(start);
        pipelineJobDto.setTid(templateId);
        return feignExecute(() -> jobFeign.createByTemplate(templateId, pipelineJobDto));
    }

    @Override
    public Page<JobDto> listByPage(int page, int pageSize, PipelinePageDto pipelinePageDto) {
        BaseQuery<PipelinePageDto> query = new BaseQuery<>(pipelinePageDto);
        query.setPageNo(page);
        query.setSize(pageSize);
        return feignExecute(() -> jobFeign.getPage(query));
    }

    @Override
    public Integer countBuild(List<Long> ids, String startTime, String endTime) {
        CountBuildQuery query = new CountBuildQuery();
        query.setIds(ids);
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        return feignExecute(() -> jobFeign.countBuild(query));
    }

    @Override
    public Boolean createMetadata(Long jobId, Map<String, Object> metadata) {
        DevopsMetadataDTO dto = new DevopsMetadataDTO();
        dto.setJobId(jobId);
        dto.setType(1);
        dto.setParameters(metadata);
        return feignExecute(() -> jobFeign.createMetadata(dto));
    }

    @Override
    public void stopBuild(Long jobId) {
        feignExecute(() -> jobFeign.stopBuild(jobId));
    }


    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_PIPELINE_FAIL;
    }


    @Override
    public List<JenkinsFileStartParameter> getRunStartParams(Long jobId) {
        List<JenkinsFileStartParameter> o = feignExecute(() -> jobFeign.runStartParams(jobId));
        if (CollectionUtils.isEmpty(o)) {
            return new ArrayList<>();
        }
        return o;
    }

    @Override
    public List<JenkinsFileStartParameter> getRunStartParams(Long jobId, Boolean lastFlag) {
        List<JenkinsFileStartParameter> o = feignExecute(() -> jobFeign.runSystemStartParams(jobId, lastFlag));
        if (CollectionUtils.isEmpty(o)) {
            return new ArrayList<>();
        }
        return o;
    }

    @Override
    public void deleteBing(List<Long> jobIds) {
        if (CollectionUtils.isEmpty(jobIds)) {
            return;
        }
        feignExecute(() -> jobFeign.deleteSystemBing(jobIds));
    }

    @Override
    public Long replayJob(Long buildId, String runDescribe) {
        ReplayDto replayDto = new ReplayDto();
        replayDto.setRunDescribe(runDescribe);
        return feignExecute(() -> jobFeign.replayJob(buildId, buildId, replayDto));
    }
}
