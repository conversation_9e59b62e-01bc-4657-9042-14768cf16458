package cn.harmonycloud.development.outbound.api.dto.pipeline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName DoDeployEnv
 * @Description 应用部署环境实体
 * <AUTHOR>
 * @Date 2020-07-10 10:01
 **/
@Data
public class DoDeployEnv {

    private Long id;
    private String name;
    private Date createdDate;
    private Date updatedDate;
    private Long createdBy;
    private Long updatedBy;
    private String comment;
    private String envCode;
    private Long key;

}