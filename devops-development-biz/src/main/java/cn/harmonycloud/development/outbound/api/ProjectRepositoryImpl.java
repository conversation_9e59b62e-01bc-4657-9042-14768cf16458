package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.ProjectRepository;
import cn.harmonycloud.development.outbound.api.dto.project.ProjectManagementDto;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.service.mapstruct.ProjectMapstruct;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;

import cn.harmonycloud.issue.model.dto.v2.ProjectQueryDTO;
import cn.harmonycloud.issue.model.vo.v2.ProjectBaseVO;
import cn.harmonycloud.issue.provider.ProjectProviderV2;
import cn.harmonycloud.project.model.vo.ProjectQueryVO;
import cn.harmonycloud.project.provider.ProjectProvider;
import cn.harmonycloud.tenant.TenantContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/9 11:38 上午
 **/
@Service
public class ProjectRepositoryImpl implements ProjectRepository, ApiRepository {

    @Value("${devops-project-svc.apiVersion:v2}")
    private String apiVersion;

    @Autowired
    private ProjectProvider projectProvider;

    @Autowired
    private ProjectProviderV2 projectProviderV2;

    @Autowired
    private ProjectMapstruct projectMapstruct;

    @Override
    public List<ProjectManagementDto> getBaseProjects(List<Long> ids){
        if("v2".equals(apiVersion)){
            ProjectQueryDTO query = new ProjectQueryDTO();
            query.setProjectIdList(ids);
            query.setTenantId(TenantContextHolder.getTenantId());
            List<ProjectQueryVO> list= feignExecute(()-> projectProviderV2.queryBaseList(query));
            return list.stream().map(project -> projectMapstruct.v2ToProject(project)).collect(Collectors.toList());
        }
        //todo
//        else if("v1".equals(apiVersion)){
//            ProjectQueryDTO query = new ProjectQueryDTO();
//            query.setProjectIdList(ids);
//            query.setTenantId(TenantContextHolder.getTenantId());
//            List<ProjectQueryVO> list= feignExecute(()-> projectProvider.queryBaseList(query));
//            return list.stream().map(project -> projectMapstruct.v1ToProject(project)).collect(Collectors.toList());
//        }
        throw new SystemException(ExceptionCode.REMOTE_PROJECT_FAIL, "api版本错误");
    }

    @Override
    public List<ProjectManagementDto> getAllProjects(List<Long> ids){
        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        List<Long> collect = ids.stream().distinct().collect(Collectors.toList());
        if("v2".equals(apiVersion)){
            ProjectQueryDTO query = new ProjectQueryDTO();
            query.setProjectIdList(collect);
            query.setTenantId(TenantContextHolder.getTenantId());
            List<ProjectQueryVO> list= feignExecute(()-> projectProviderV2.queryAll(query));
            return list.stream().map(project -> projectMapstruct.v2ToProject(project)).collect(Collectors.toList());
        }else if("v1".equals(apiVersion)){
            ProjectQueryDTO query = new ProjectQueryDTO();
            query.setProjectIdList(collect);
            query.setTenantId(TenantContextHolder.getTenantId());
//todo
         //   List<ProjectQueryVO> list= feignExecute(()-> projectProvider.queryAll(query));
          //  return list.stream().map(project -> projectMapstruct.v1ToProject(project)).collect(Collectors.toList());
            return new ArrayList<>();
        }
        throw new SystemException(ExceptionCode.REMOTE_PROJECT_FAIL, "api版本错误");
    }

    @Override
    public Map<Long, ProjectManagementDto> mapAllProjects(List<Long> ids){
        List<ProjectManagementDto> allProjects = this.getAllProjects(ids);
        return allProjects.stream().collect(Collectors.toMap(ProjectManagementDto::getId, p -> p));
    }

    @Override
    public Map<Long, ProjectManagementDto> mapBaseProjects(List<Long> ids) {
        List<ProjectManagementDto> allProjects = this.getBaseProjects(ids);
        return allProjects.stream().collect(Collectors.toMap(ProjectManagementDto::getId, p -> p));
    }

    @Override
    public ProjectManagementDto getById(Long projectId){
        if("v2".equals(apiVersion)){
            ProjectQueryVO projectQueryVO = feignExecute(()-> projectProviderV2.getById(projectId.toString()));
            if(projectQueryVO == null ){
                return null;
            }
            return projectMapstruct.v2ToProject(projectQueryVO);
        }else if("v1".equals(apiVersion)){
            ProjectQueryVO projectQueryVO = feignExecute(()-> projectProvider.getById(projectId.toString()));
            if(projectQueryVO == null ){
                return null;
            }
            return projectMapstruct.v1ToProject(projectQueryVO);
        }
        throw new SystemException(ExceptionCode.REMOTE_PROJECT_FAIL, "api版本错误");
    }

    @Override
    public List<ProjectManagementDto> getByUser() {
        //todo
//        if("v2".equals(apiVersion)){
//            List<ProjectBaseVO> data =  feignExecute(()-> projectProviderV2.getByUser());
//            return data.stream().map(project -> projectMapstruct.v2ToProject(project)).collect(Collectors.toList());
//
//        }else if("v1".equals(apiVersion)) {
//            List<cn.harmonycloud.issue.model.vo.ProjectBaseVO.ProjectBaseVO> data = feignExecute(() -> projectProvider.getByUser());
//            return data.stream().map(project -> projectMapstruct.v1ToProject(project)).collect(Collectors.toList());
//        }
//        throw new SystemException(ExceptionCode.REMOTE_PROJECT_FAIL, "api版本错误");
        return new ArrayList<>();
    }

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_PROJECT_FAIL;
    }
}
