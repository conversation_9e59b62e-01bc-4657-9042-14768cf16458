package cn.harmonycloud.development.outbound.api.dto.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/11 10:01 上午
 **/
@Data
@ApiModel("配置环境创建")
public class ConfigEnvCreateDTO {

    @ApiModelProperty("配置id")
    private Long configId;

    @ApiModelProperty("环境名称")
    private String envName;

    public ConfigEnvCreateDTO(Long configId, String envName){
        this.configId = configId;
        this.envName = envName;
    }

}
