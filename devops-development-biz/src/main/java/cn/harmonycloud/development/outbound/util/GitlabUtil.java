package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.config.GitSyncDTO;
import cn.harmonycloud.development.constant.GitlabAccessLevelEnum;
import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.enums.ExceptionCode;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.TransportException;
import org.eclipse.jgit.transport.CredentialsProvider;
import org.eclipse.jgit.transport.TransportHttp;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.eclipse.jgit.util.FileUtils;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.models.Project;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class GitlabUtil {

    public static final String ORIGIN = "origin";
    public static final String OAUTH2_USERNAME = "oauth2"; // GitLab要求的固定用户名
    public static final String PRIVATE_TOKEN = "PRIVATE-TOKEN";
    public static final String UTF_8 = "UTF-8";
    public static final String PAGE_URL = "%s://%s/api/v4/projects?search=%s&search_namespaces=true&per_page=10000";
    public static final String GIT = ".git";
    public static final String GIT_PULL = "git-pull-";
    public static final String GIT_PUSH = "git-push-";
    public static final String API_PROJECTS_BY_GROUP = "%s/api/v4/groups/%s/projects?per_page=10000&with_shared=false";

    public static final String FRONTEND_NAME = "trina-frontend-web";
    public static final String BACKEND_NAME = "trina-backend";
    public static final String BACKEND_DEFAULT_PACKAGE_NAME = "com.trinasolar.backend";

    /**
     * 查询子组下的所有Git项目路径（HTTP格式）
     *
     * @param subgroupPath 子组路径（如：parent-group/sub-group）
     * @param privateToken GitLab访问令牌
     * @param gitlabDomain GitLab域名（如：gitlab.example.com）
     * @return 项目Git路径列表（HTTP格式）
     * @throws Exception 请求异常或解析异常
     */
    public static List<String> getSubgroupProjectsGitList(String subgroupPath, String privateToken, String gitlabDomain) {
        // 构造API URL
        String encodedSubgroup = URLEncoder.encode(subgroupPath, StandardCharsets.UTF_8).replace("+", "%20");
        String apiUrl = String.format(API_PROJECTS_BY_GROUP, gitlabDomain, encodedSubgroup);

        HttpGet get = new HttpGet(apiUrl);
        get.setHeader(PRIVATE_TOKEN, privateToken);

        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(get)) {

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                String errorBody = EntityUtils.toString(response.getEntity());
                log.error("查询子组项目失败，状态码：{}，响应：{}", statusCode, errorBody);
                throw new GitException("查询子组项目失败，状态码：" + statusCode);
            }

            String responseBody = EntityUtils.toString(response.getEntity());
            JSONArray projects = JSON.parseArray(responseBody);
            return projects.stream()
                    .map(JSONObject.class::cast)
                    .map(project -> project.getString("http_url_to_repo"))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取组下用户异常！", e);
            //todo 临时处理，不让前端报错
            // throw new GitException("获取组下用户异常！" + e.getMessage());
            return new ArrayList<>();
        }
    }


    /**
     * 拉取远程仓库内容到本地
     *
     * @param git                 本地缓存
     * @param credentialsProvider 鉴权密码
     */
    public static void gitPull(Git git, CredentialsProvider credentialsProvider) throws Exception {
        git.pull().setRemote(ORIGIN).setCredentialsProvider(credentialsProvider).call();
    }

    // 仓库创建方法
    public static void createGitlabRepositoryIfNotExists(String repoUrl, String pushPrivateToken) throws Exception {
        UsernamePasswordCredentialsProvider credentials = GitlabUtil.createTokenCredentials(pushPrivateToken);
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 新增请求参数验证逻辑
            if (repoUrl == null || credentials == null || pushPrivateToken == null) {
                log.error("创建仓库时请求参数缺失，repoUrl: {}, credentials: {}, pushPrivateToken: {}", repoUrl, credentials, pushPrivateToken);
                throw new IllegalArgumentException("创建仓库时请求参数缺失");
            }
            // 加强仓库存在性检查
            Git.lsRemoteRepository()
                    .setRemote(repoUrl)
                    .setCredentialsProvider(credentials)
                    .setTimeout(30)
                    .call();
        } catch (TransportException e) {
            URI uri = URI.create(repoUrl);
            String apiBase = String.format("%s://%s/api/v4", uri.getScheme(), uri.getAuthority());

            // 新增路径解析逻辑
            String fullPath = uri.getPath().replace(".git", "").substring(1);
            String[] pathSegments = fullPath.split("/");
            String projectName = pathSegments[pathSegments.length - 1];
            String namespacePath = String.join("/", Arrays.copyOf(pathSegments, pathSegments.length - 1));

            HttpPost post = new HttpPost(apiBase + "/projects");
            post.setHeader("PRIVATE-TOKEN", pushPrivateToken);
            post.setHeader("Content-Type", "application/json");

            // 请求体生成逻辑
            int namespaceId = getNamespaceId(apiBase, pushPrivateToken, namespacePath); // 新增动态获取方法
            String jsonBody = String.format(
                    "{\"name\": \"%s\", \"path\": \"%s\", \"namespace_id\": %d, \"visibility\": \"private\"}",
                    projectName,
                    projectName,
                    namespaceId  // 使用动态获取的ID
            );

            post.setEntity(new StringEntity(jsonBody));

            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                CloseableHttpResponse response = httpClient.execute(post);
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode != HttpStatus.SC_CREATED) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    // 新增错误处理逻辑，记录详细日志并抛出异常
                    log.error("请求URL: {}", post.getURI());
                    log.error("请求体: {}", jsonBody);
                    throw new GitException("仓库创建失败，HTTP状态码：" + statusCode + "，响应内容：" + responseBody);
                }
                log.info("成功创建仓库：{}", repoUrl);
            }
        }
    }

    // 修改鉴权提供方法
    private static UsernamePasswordCredentialsProvider createTokenCredentials(String privateToken) {
        return new UsernamePasswordCredentialsProvider(OAUTH2_USERNAME, privateToken);
    }


    /**
     * 同步Git代码，从源仓库拉取代码并推送到目标仓库
     *
     * @param gitSyncDTO 同步配置参数
     * @param isTemplate 是否是脚手架
     * @param tempType   脚手架类型
     * @throws Exception
     */
    public static void syncGitCode(GitSyncDTO gitSyncDTO, boolean isTemplate, String templateType) throws Exception {
        // 提取参数
        String pullGitUrl = gitSyncDTO.getPullGitUrl();
        String pushGitUrl = gitSyncDTO.getPushGitUrl();
        String pullBranch = gitSyncDTO.getPullBranch();
        String pushBranch = gitSyncDTO.getPushBranch();

        // 创建凭证
        UsernamePasswordCredentialsProvider pullCredentials = createTokenCredentials(gitSyncDTO.getPullPrivateToken());
        UsernamePasswordCredentialsProvider pushCredentials = createTokenCredentials(gitSyncDTO.getPushPrivateToken());

        // 确保目标仓库存在
        createGitlabRepositoryIfNotExists(pushGitUrl, gitSyncDTO.getPushPrivateToken());

        // 创建临时目录
        File tempRoot = new File(System.getProperty("java.io.tmpdir"), "git-sync-" + System.currentTimeMillis());
        tempRoot.mkdirs();
        File pullDir = new File(tempRoot, "source");
        File pushDir = new File(tempRoot, "target");

        try {
            // 克隆源仓库和目标仓库
            Git pullGit = Git.cloneRepository().setURI(pullGitUrl).setBranch(pullBranch).setDirectory(pullDir)
                    .setCredentialsProvider(pullCredentials).setTimeout(30).call();

            Git pushGit = Git.cloneRepository().setURI(pushGitUrl).setBranch(pushBranch).setDirectory(pushDir)
                    .setCredentialsProvider(pushCredentials).setTimeout(30).call();

            try {
                // 清空目标仓库（保留.git目录）
                Arrays.stream(pushDir.listFiles())
                        .filter(file -> !".git".equals(file.getName()))
                        .forEach(file -> {
                            try {
                                FileUtils.delete(file, FileUtils.RECURSIVE);
                            } catch (IOException e) {
                                log.error("清空目标目录失败", e);
                            }
                        });
                // 复制源仓库内容到目标仓库
                copyCodePullToPush(pullDir, pushDir, isTemplate, templateType, gitSyncDTO);
                // 提交并推送
                pushGit.add().addFilepattern(".").call();
                pushGit.commit().setMessage("同步提交 - " + new java.util.Date()).call();
                pushGit.push().setForce(true).setCredentialsProvider(pushCredentials).call();

                log.info("成功同步代码从 {} 到 {}", pullGitUrl, pushGitUrl);
            } finally {
                pullGit.close();
                pushGit.close();
            }
        } finally {
            // 清理临时目录
            FileUtils.delete(tempRoot, FileUtils.RECURSIVE);
        }
    }

    /**
     * 复制源代码到目标目录，并处理前端配置文件中的项目名称替换
     *
     * @param pullDir    源目录
     * @param pushDir    目标目录
     * @param gitSyncDTO 同步配置参数
     * @throws IOException 文件操作异常
     */
    private static void copyCodePullToPush(File pullDir, File pushDir, boolean isTemplate, String templateType,
                                           GitSyncDTO gitSyncDTO) throws IOException {
        String programNameEn = gitSyncDTO.getProgramNameEn();
        String packageName = gitSyncDTO.getPackageName();
        Path sourcePath = pullDir.toPath();
        Path targetPath = pushDir.toPath();

        // 用于存储需要重命名的目录映射
        Map<Path, Path> directoriesToRename = new HashMap<>();
        if (isTemplate && "VUE".equals(templateType)) {
            // 前端模板模式：执行文件内容替换和目录重命名
            log.info("使用前端模板模式，将替换项目名称和重命名目录");

            // 第一步：遍历源目录，复制文件并收集需要重命名的目录
            copyFilesAndCollectDirectories(sourcePath, targetPath, programNameEn, directoriesToRename);

            // 第二步：重命名收集到的目录
            renameDirectories(directoriesToRename);
        } else if (isTemplate && "Springboot".equals(templateType)) {
            // 后端模板模式：修改后端文件内容和包名
            log.info("使用后端模板模式，将替换项目名称和包名");

            // 使用默认包名或用户提供的包名
            String targetPackage = StringUtils.isEmpty(packageName) ?
                    BACKEND_DEFAULT_PACKAGE_NAME : packageName;

            // 处理后端代码
            processBackendCode(sourcePath, targetPath, programNameEn, targetPackage);
        } else {
            // 非模板模式：简单复制文件，不执行替换和重命名
            log.info("使用普通模式，直接复制文件");
            simpleCopyFiles(sourcePath, targetPath);
        }
    }

    /**
     * 遍历源目录，复制文件并收集需要重命名的目录
     */
    private static void copyFilesAndCollectDirectories(Path sourcePath, Path targetPath, String programNameEn,
                                                       Map<Path, Path> directoriesToRename) throws IOException {
        // 需要处理的前端配置文件列表
        List<String> frontendConfigFiles = Arrays.asList(
                ".env", ".env.development", ".env.production", ".env.test", "package.json"
        );

        Files.walk(sourcePath)
                .filter(path -> !path.toString().contains(".git"))
                .forEach(source -> {
                    try {
                        // 处理路径替换
                        Path relativePath = processRelativePath(sourcePath, source, programNameEn);
                        Path dest = targetPath.resolve(relativePath);

                        if (Files.isDirectory(source)) {
                            handleDirectory(source, dest, programNameEn, directoriesToRename);
                        } else {
                            handleFile(source, dest, relativePath, frontendConfigFiles, programNameEn);
                        }
                    } catch (IOException e) {
                        log.error("复制文件失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "复制文件失败: " + e.getMessage());
                    }
                });
    }

    /**
     * 处理相对路径，替换项目名称
     */
    private static Path processRelativePath(Path sourcePath, Path source, String programNameEn) {
        Path relativePath = sourcePath.relativize(source);
        String pathStr = relativePath.toString();

        // 检查路径中是否包含需要替换的项目名称
        if (pathStr.contains(FRONTEND_NAME)) {
            // 创建新的相对路径，替换项目名称
            String newPathStr = pathStr.replace(FRONTEND_NAME, programNameEn);
            relativePath = Paths.get(newPathStr);
            log.info("重命名路径: {} -> {}", pathStr, newPathStr);
        }

        return relativePath;
    }

    /**
     * 处理目录，创建目录并收集需要重命名的目录
     */
    private static void handleDirectory(Path source, Path dest, String programNameEn,
                                        Map<Path, Path> directoriesToRename) throws IOException {
        // 创建目标目录
        if (!Files.exists(dest)) {
            Files.createDirectories(dest);
        }

        // 如果目录名包含项目名称，记录下来以便后续重命名
        if (source.getFileName().toString().equals(FRONTEND_NAME)) {
            Path newDest = dest.getParent().resolve(programNameEn);
            directoriesToRename.put(dest, newDest);
        }
    }

    /**
     * 处理文件，替换内容或直接复制
     */
    private static void handleFile(Path source, Path dest, Path relativePath,
                                   List<String> frontendConfigFiles, String programNameEn) throws IOException {
        // 确保父目录存在
        Files.createDirectories(dest.getParent());

        // 检查是否需要处理的前端配置文件
        String fileName = source.getFileName().toString();
        if (frontendConfigFiles.contains(fileName)) {
            processConfigFile(source, dest, relativePath, programNameEn);
        } else {
            // 非配置文件，直接复制
            Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    /**
     * 处理配置文件，替换内容中的项目名称
     */
    private static void processConfigFile(Path source, Path dest, Path relativePath,
                                          String programNameEn) throws IOException {
        // 读取文件内容
        String content = new String(Files.readAllBytes(source), StandardCharsets.UTF_8);

        // 替换项目名称
        if (content.contains(FRONTEND_NAME)) {
            content = content.replace(FRONTEND_NAME, programNameEn);
            // 写入修改后的内容
            Files.write(dest, content.getBytes(StandardCharsets.UTF_8));
            log.info("已替换文件 {} 中的项目名称为: {}", relativePath, programNameEn);
        } else {
            // 无需替换，直接复制
            Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    /**
     * 重命名收集到的目录
     */
    private static void renameDirectories(Map<Path, Path> directoriesToRename) {
        // 从深层次目录开始，避免父目录重命名后找不到子目录
        directoriesToRename.entrySet().stream()
                .sorted((e1, e2) -> e2.getKey().toString().length() - e1.getKey().toString().length())
                .forEach(entry -> {
                    try {
                        Path oldPath = entry.getKey();
                        Path newPath = entry.getValue();

                        if (Files.exists(oldPath) && !Files.exists(newPath)) {
                            Files.move(oldPath, newPath);
                            log.info("重命名目录: {} -> {}", oldPath, newPath);
                        }
                    } catch (IOException e) {
                        log.error("重命名目录失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "重命名目录失败: " + e.getMessage());
                    }
                });
    }

    /**
     * 简单复制文件，不执行替换和重命名
     */
    private static void simpleCopyFiles(Path sourcePath, Path targetPath) throws IOException {
        Files.walk(sourcePath)
                .filter(path -> !path.toString().contains(".git"))
                .forEach(source -> {
                    try {
                        Path relativePath = sourcePath.relativize(source);
                        Path dest = targetPath.resolve(relativePath);

                        if (Files.isDirectory(source)) {
                            if (!Files.exists(dest)) {
                                Files.createDirectories(dest);
                            }
                        } else {
                            Files.createDirectories(dest.getParent());
                            Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                        }
                    } catch (IOException e) {
                        log.error("复制文件失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "复制文件失败: " + e.getMessage());
                    }
                });
    }

    // 新增动态获取命名空间ID方法
    private static int getNamespaceId(String apiBase, String token, String namespacePath) throws Exception {
        HttpGet get = new HttpGet(apiBase + "/groups/" + URLEncoder.encode(namespacePath, "UTF-8"));
        get.setHeader(PRIVATE_TOKEN, token);

        try (CloseableHttpResponse response = HttpClients.createDefault().execute(get)) {
            JSONObject json = JSON.parseObject(EntityUtils.toString(response.getEntity()));
            return json.getIntValue("id");
        }
    }


    public static void addUsersToProject(String url, String privateToken, List<String> usernames) {
        URI uri = URI.create(url);
        String path = uri.getPath();
        String domain = uri.getScheme() + "://" + uri.getHost();
        String projectPath = path.substring(1).replace(".git", "");
        try (GitLabApi gitLabApi = new GitLabApi(domain, privateToken)) {
            Project project = gitLabApi.getProjectApi().getProject(projectPath);
            for (String username : usernames) {
                gitLabApi.getUserApi().getOptionalUser(username).ifPresent(e -> {
                    try {
                        gitLabApi.getProjectApi().addMember(project.getId(), e.getId(), GitlabAccessLevelEnum.DEVELOPER.getLevel());
                    } catch (Exception ex) {
                        log.error("添加用户:【{}】异常", username, ex);
                    }
                });
            }
        } catch (Exception e) {
            log.error("批量添加用户到GitLab项目失败", e);
        }
    }

    public static void removeUsersFromProject(String url, String privateToken, List<String> usernames) {
        URI uri = URI.create(url);
        String path = uri.getPath();
        String domain = uri.getScheme() + "://" + uri.getHost();
        String projectPath = path.substring(1).replace(".git", "");
        try (GitLabApi gitLabApi = new GitLabApi(domain, privateToken)) {
            Project project = gitLabApi.getProjectApi().getProject(projectPath);
            for (String username : usernames) {
                gitLabApi.getUserApi().getOptionalUser(username).ifPresent(e -> {
                    try {
                        gitLabApi.getProjectApi().removeMember(project.getId(), e.getId());
                    } catch (Exception ex) {
                        log.error("移除用户:【{}】异常", username, ex);
                    }
                });
            }
        } catch (Exception e) {
            log.error("批量从GitLab项目移除用户失败", e);
        }
    }

    /**
     * 生产用master 开发用develop 测试和预生产用release
     *
     * @param pushGitUrl
     * @param pushPrivateToken
     * @param master
     */
    public static void create4EnvBranch(String pushGitUrl, String pushPrivateToken, String master) {
        log.warn("pushGitUrl is {}", pushGitUrl);
        log.warn("pushPrivateToken is {}", pushPrivateToken);
        log.warn("master is {}", master);
        try {
            URI uri = URI.create(pushGitUrl);
            String domain = uri.getScheme() + "://" + uri.getHost();
            String path = uri.getPath();
            String projectPath = path.substring(1).replace(".git", "");
            log.warn("projectPath is {}", projectPath);
            // 使用正确的参数创建 GitLabApi 实例
            try (GitLabApi gitLabApi = new GitLabApi(domain, pushPrivateToken)) {
                // 获取项目ID或路径
                Project project = gitLabApi.getProjectApi().getProject(projectPath);
                Integer projectId = project.getId();
                // 使用项目ID创建分支
                gitLabApi.getRepositoryApi().createBranch(projectId, "develop", master);
                gitLabApi.getRepositoryApi().createBranch(projectId, "release", master);
                log.info("成功为项目 {} 创建2个环境分支", projectPath);
            }
        } catch (Exception e) {
            log.error("创建4个环境分支失败: {}", e.getMessage(), e);
            throw new GitException("创建环境分支失败: " + e.getMessage());
        }
    }

    /**
     * 处理后端代码，替换包名和项目名称
     */
    private static void processBackendCode(Path sourcePath, Path targetPath, String programNameEn,
                                           String targetPackage) throws IOException {
        // 源包名和目标包名
        String sourcePackage = BACKEND_DEFAULT_PACKAGE_NAME;

        // 源包路径和目标包路径（将点号替换为路径分隔符）
        String sourcePackagePath = sourcePackage.replace('.', File.separatorChar);
        String targetPackagePath = targetPackage.replace('.', File.separatorChar);

        // 需要处理的后端配置文件
        List<String> backendConfigFiles = Arrays.asList(
                "pom.xml", "application.yml", "application-dev.yml"
        );

        // 需要处理的Java文件扩展名
        List<String> javaExtensions = Arrays.asList(".java");

        // 用于存储需要重命名的目录映射
        Map<Path, Path> directoriesToRename = new HashMap<>();

        // 遍历源目录中的所有文件
        Files.walk(sourcePath)
                .filter(path -> !path.toString().contains(".git"))
                .forEach(source -> {
                    try {
                        // 获取相对路径
                        Path relativePath = sourcePath.relativize(source);
                        String pathStr = relativePath.toString();

                        // 替换项目名称
                        if (pathStr.contains(BACKEND_NAME)) {
                            pathStr = pathStr.replace(BACKEND_NAME, programNameEn);
                        }

                        // 替换包路径
                        if (pathStr.contains(sourcePackagePath)) {
                            pathStr = pathStr.replace(sourcePackagePath, targetPackagePath);
                        }

                        // 创建新的相对路径
                        Path newRelativePath = Paths.get(pathStr);
                        Path dest = targetPath.resolve(newRelativePath);

                        if (Files.isDirectory(source)) {
                            // 创建目标目录
                            if (!Files.exists(dest)) {
                                Files.createDirectories(dest);
                            }

                            // 如果目录名包含项目名称，记录下来以便后续重命名
                            if (source.getFileName().toString().equals(BACKEND_NAME)) {
                                Path newDest = dest.getParent().resolve(programNameEn);
                                directoriesToRename.put(dest, newDest);
                            }
                        } else {
                            // 确保父目录存在
                            Files.createDirectories(dest.getParent());

                            // 获取文件名和扩展名
                            String fileName = source.getFileName().toString();
                            String fileExtension = getFileExtension(fileName);

                            // 判断是否需要处理内容
                            boolean isConfigFile = backendConfigFiles.contains(fileName);
                            boolean isJavaFile = javaExtensions.contains(fileExtension);

                            if (isConfigFile || isJavaFile) {
                                // 读取文件内容
                                String content = new String(Files.readAllBytes(source), StandardCharsets.UTF_8);
                                boolean contentChanged = false;

                                // 替换包名
                                if (content.contains(sourcePackage)) {
                                    content = content.replace(sourcePackage, targetPackage);
                                    contentChanged = true;
                                }

                                // 替换项目名称
                                if (content.contains(BACKEND_NAME)) {
                                    content = content.replace(BACKEND_NAME, programNameEn);
                                    contentChanged = true;
                                }

                                if (contentChanged) {
                                    // 写入修改后的内容
                                    Files.write(dest, content.getBytes(StandardCharsets.UTF_8));
                                    log.info("已替换文件 {} 中的内容", newRelativePath);
                                } else {
                                    // 无需替换，直接复制
                                    Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                                }
                            } else {
                                // 非需要处理的文件，直接复制
                                Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                            }
                        }
                    } catch (IOException e) {
                        log.error("处理后端代码失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "处理后端代码失败: " + e.getMessage());
                    }
                });

        // 重命名收集到的目录
        renameDirectories(directoriesToRename);
    }

    /**
     * 获取文件扩展名（包含点号）
     */
    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }
}

