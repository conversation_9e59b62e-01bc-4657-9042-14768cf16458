package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.vo.feature.FeatureListQuery;
import cn.harmonycloud.development.pojo.vo.feature.FeaturePageQuery;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDateTime;
import java.util.List;

public interface FeatureRepository extends BaseRepository<DevopsFeature> {

    /**
     * 分页查询特性列表
     *
     * @param request
     * @return
     */
    Page<DevopsFeature> page(FeaturePageQuery request);


    /**
     * 特性列表
     * @param query
     * @return
     */
    List<DevopsFeature> list(FeatureListQuery query);

    /**
     * 特性列表
     *
     * @param systemId 系统id
     * @return
     */
    List<DevopsFeature> listBySystemId(Long systemId);

    /**
     * 特性列表
     *
     * @param subIds 系统id
     * @return
     */
    List<DevopsFeature> listBySubSystemIds(List<Long> subIds);

    int maxFeatureNumberBySubId(Long subsystemId);

    void removeLogic(Long id, boolean b, User currentUser);

    /**
     * 发布标记（标记特性，记录版本发布时间）
     *
     * @param featureIds
     */
    void releaseMark(List<Long> featureIds, User currentUser, LocalDateTime now);

    /**
     * 查询发布时间前的数据
     *
     * @param clearTime
     * @param clearFlag
     */
    List<DevopsFeature> listBeforeReleaseTime(LocalDateTime clearTime, int clearFlag);

    /**
     * 更新特性状态
     *
     * @param id
     * @param status
     * @param currentUserId
     */
    void updateStatus(Long id, Integer status, Long currentUserId);

    /**
     * 分支清理
     *
     * 将状态修改为 FeatureStatusEnum.CLEAR
     * 将clear_status 修改为 2-清理完成
     *
     * @param id
     * @param currentUserId
     */
    void clear(Long id, Long currentUserId);
}
