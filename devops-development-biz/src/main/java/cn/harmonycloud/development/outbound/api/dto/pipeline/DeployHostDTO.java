package cn.harmonycloud.development.outbound.api.dto.pipeline;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/28 3:04 下午
 **/
@Data
public class DeployHostDTO implements Serializable {

    private Integer id;
    private String name;
    private String ip;
    private Integer port;

    private Integer type;
    private Integer appId;
    private Integer credentialId;
    private Integer envId;
    /**
     * 密码类型
     * 0-用户名密码
     * 1-ssh
     * 2-token
     * 3-kubeconfig
     * 4-凭据
     * 5-观云台
     */
    private Integer pwdType;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private Long createdBy;
    private Long updatedBy;
}
