package cn.harmonycloud.development.outbound.api.dto.scm;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/24 10:49 上午
 **/
@Data
public class MergeTaskRequest implements Serializable {

    private static final long serialVersionUID = -5784153904956772484L;
    private Long groupId; // 分组id
    private List<String> branches; // 源分支列表
    private Integer gitlabId; // gitlabid
    private String groupName; // 分组名称
    private String sourceBranch; // 基础分值
    private String target; // 目标分支
    private Boolean isTargetDynamic; // true表是动态生成新分支。
}
