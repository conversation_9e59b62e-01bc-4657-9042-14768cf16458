package cn.harmonycloud.development.outbound.api.dto.coderepo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/26 5:57 下午
 **/
@Data
public class BranchStageDto {

    @ApiModelProperty("领先主分支commit数量")
    private Integer ahead = 0;

    @ApiModelProperty("落后主分支commit数量")
    private Integer behind = 0;

    private String name;

    private String baseBranch;
}
