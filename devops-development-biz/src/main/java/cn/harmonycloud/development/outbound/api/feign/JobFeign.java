package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JobPermission;
import cn.harmonycloud.development.outbound.api.dto.pipeline.PipelineBindingDto;
import cn.harmonycloud.development.config.CloudFeignConfiguration;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.pojo.vo.env.EnvVariableVO;
import cn.harmonycloud.development.pojo.vo.env.VariableVO;
import cn.harmonycloud.development.outbound.api.dto.pipeline.*;
import cn.harmonycloud.mybatis.base.BaseQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(name = "pipelineFeign", url = "${cloud.pipeline.url}",configuration = {FeignConfig.class, CloudFeignConfiguration.class})
public interface JobFeign {

    /**
     * 根据jobid集合查询流水线列表
     * @param idList
     * @param resourceFlag true-当前登录人可查看的流水线
     * @return
     */
    @PostMapping("/api/open/pipeline/list")
    BaseResult<List<JobDto>> listByIds(@RequestBody List<String> idList, @RequestParam Boolean resourceFlag, @RequestParam String draftStatus);

    /**
     * 执行流水线构建接口
     * @param buildDTO
     * @return
     */
    @PostMapping("/devops/pipeline/buildReturnId")
    BaseResult<Long> buildJob(@RequestBody BuildDto buildDTO);

    /**
     * 执行流水线构建接口
     *
     * @param
     * @return
     */
    @PostMapping("/devops/pipeline/replay/{id}")
    BaseResult<Long> replayJob(@PathVariable Long id,
                              @RequestParam Long buildId,
                              @RequestBody ReplayDto replayDto);

    /**
     * 停止流水线
     * @param jobId
     * @return
     */
    @PostMapping("/devops/pipeline/{jobId}/build/stop")
    BaseResult stopBuild(@PathVariable Long jobId);

    /**
     * 查看流水线最近一次构建详情
     * @param jobId
     * @return
     */
    @GetMapping("/devops/pipeline/{jobId}/builds/recent")
    BaseResult<BuildDetailDto> getRecentBuildByJobId(@PathVariable Long jobId);

    /**
     * 根据buildid查询构建详情
     * @param buildId
     * @return
     */
    @GetMapping("/devops/pipeline/builds/{buildId}")
    BaseResult<BuildDetailDto> getRecentBuildByBuildId(@PathVariable Long buildId);

    /**
     * 根据buildid和job查询构建详情，build为null时表示未构建
     * @param buildId
     * @return
     */
    @GetMapping("/devops/pipeline/builds/detail")
    BaseResult<BuildDetailDto> getRecentBuildDetail(@RequestParam Long jobId,
                                                    @RequestParam(required = false) Long buildId);

    /**
     * 根据模板创建一条流水线
     * @param tid
     * @param tenant
     * @return
     */
    @PostMapping("/devops/pipeline/jobs/{tid}")
    BaseResult<Long> createByTemplate(@PathVariable Long tid,@RequestBody PipelineJobDto tenant);

    /**
     * 分页查询流水线列表
     * @param query
     * @return
     */
    @PostMapping("/devops/pipeline/jobs/queryFirst")
    BaseResult<Page<JobDto>> getPage(@RequestBody BaseQuery<PipelinePageDto> query);

    /**
     * 分页查询流水线列表
     * @param request
     * @return
     */
    @PostMapping("/devops/metadata/relate")
    BaseResult<Boolean> createMetadata(@RequestBody DevopsMetadataDTO request);

    /**
     * 获取所有环境和变量
     * @return
     */
    @GetMapping("/deploy/env/variables/all")
    BaseResult<List<EnvVariableVO>> getAllEnvVariable();

    @GetMapping("/envVar")
    BaseResult<Page<VariableVO>> getVarByEnvId(@RequestParam Integer envId,
                                               @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                               @RequestParam(required = false, defaultValue = "10") Integer sizeNum);

    /**
     * 统计流水线构建数量
     * @return
     */
    @GetMapping("/devops/pipeline/builds/countBuild")
    BaseResult<Integer> countBuild(@SpringQueryMap CountBuildQuery query);

    @PostMapping("/devops/pipeline/jobs/binding")
    BaseResult binding(@RequestBody PipelineBindingDto pipelineBindingDto);


    @PostMapping("/devops/pipeline/job/system/addUser")
    BaseResult addPermission(@RequestBody JobPermission userMap);

    @PostMapping("/devops/pipeline/job/system/deleteUser")
    BaseResult deletePermission(@RequestBody JobPermission userMap);

    /**
     * 获取流水线的启动变量
     *
     * @param jobId
     * @return
     */
    @GetMapping("/devops/pipeline/jobs/{jobId}/runStartParams")
    BaseResult<List<JenkinsFileStartParameter>> runSystemStartParams(@PathVariable Long jobId,
                                                                     @RequestParam(required = false) Boolean lastFlag);

    /**
     * 获取流水线的启动变量
     *
     * @param jobId
     * @return
     */
    @PostMapping("/devops/pipeline/jobs/{jobId}/runStartParams")
    BaseResult<List<JenkinsFileStartParameter>> runStartParams(@PathVariable Long jobId);

    /**
     * 删除流水线关联
     *
     * @param jobId
     * @return
     */
    @PostMapping("/devops/pipeline/job/system/deleteSystemBing")
    BaseResult deleteSystemBing(@RequestBody List<Long> jobId);



}
