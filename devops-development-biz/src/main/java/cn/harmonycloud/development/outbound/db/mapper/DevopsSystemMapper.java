package cn.harmonycloud.development.outbound.db.mapper;

import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DevopsSystemMapper extends BaseMapper<DevopsSystem> {

    @InterceptorIgnore(tenantLine = "true")
    List<DevopsSystem> listByIdsIgnoreTenant(@Param("systemIds") List<Long> systemIds);

    List<DevopsSystem> listByProject(@Param("projectId") Long projectId);

}
