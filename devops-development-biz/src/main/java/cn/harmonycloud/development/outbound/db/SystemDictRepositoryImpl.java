package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.SystemDictRepository;
import cn.harmonycloud.development.outbound.db.mapper.SystemDictMapper;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/10 10:33 上午
 **/
@Service
public class SystemDictRepositoryImpl extends BaseRepositoryImpl<SystemDictMapper, SystemDict> implements SystemDictRepository {

    private final Map<String, List<SystemDict>> testEnv = Maps.newConcurrentMap();

    private final Map<Long, SystemDict> dictMap = Maps.newConcurrentMap();

    @PostConstruct
    private void init(){
        List<SystemDict> systemDict = this.listAll();
        Map<String, List<SystemDict>> collect = systemDict.stream().collect(Collectors.groupingBy(SystemDict::getSubject));
        Map<Long, SystemDict> collect1 = systemDict.stream().collect(Collectors.toMap(SystemDict::getId, d -> d));
        dictMap.putAll(collect1);
        testEnv.putAll(collect);
    }

    @Override
    public List<SystemDict> listAll() {
        LambdaQueryWrapper<SystemDict> query = new LambdaQueryWrapper<SystemDict>()
                .orderByAsc(SystemDict::getDictSort);
        return  this.list(query);
    }

    @Override
    public Map<String, List<SystemDict>> getAllDict(boolean useCache) {
        if(useCache){
            return testEnv;
        }
        List<SystemDict> systemDict = this.listAll();
        return systemDict.stream().collect(Collectors.groupingBy(SystemDict::getSubject));
    }

    @Override
    public List<SystemDict> getByParams(String subject) {
        List<SystemDict> systemDict = this.getAllDict(false).get(subject);
        if(systemDict == null){
            return new ArrayList<>();
        }
        return systemDict;
    }

    @Override
    public SystemDict getCacheById(Long id) {
        return dictMap.get(id);
    }
}
