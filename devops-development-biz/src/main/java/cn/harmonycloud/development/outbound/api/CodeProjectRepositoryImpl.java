package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.CodeProjectRepository;
import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.development.outbound.api.enums.LanguageEnum;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.CodeProjectFeign;
import cn.harmonycloud.development.outbound.api.dto.scm.*;
import cn.harmonycloud.development.pojo.vo.scm.GetConfigResponse;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.pmp.model.entity.User;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/17 9:25 上午
 **/
@Slf4j
@Service
public class CodeProjectRepositoryImpl implements CodeProjectRepository, ApiRepository {

    @Autowired
    private CodeProjectFeign codeProjectFeign;
    @Autowired
    private IamRepository iamRepository;
    @Value("${cloud.scm.token:SCM@2023}")
    private String token;

    @Override
    public GitProjectDto getProject(Integer gitlabId) {
        return customsHeaderFeignExecute(() -> codeProjectFeign.project(gitlabId), token);
    }

    @Override
    public ProjectResponse createProject(ProjectRequest request) {
        return customsHeaderFeignExecute(() -> codeProjectFeign.createProject(request), token);
    }

    @Override
    public void updateProject(UpdateProjectRequest request) {
        customsHeaderFeignExecute(() -> codeProjectFeign.updateProject(request), token);
    }

    @Override
    public void registry(Integer groupId, Integer gitlabId) {
        RegistryDto registryDto = new RegistryDto();
        registryDto.setGitlabId(gitlabId);
        registryDto.setGroupId(groupId);
        List<RegistryDto> registryList = new ArrayList<>();
        registryList.add(registryDto);
        feignExecute(() -> codeProjectFeign.registry(registryList));
    }

    @Override
    public List<GitProjectDto> projectList(Integer groupId) {
        return feignExecute(() -> codeProjectFeign.projectList(groupId));
    }

    @Override
    public GitProjectDto getProject(Integer groupId, String path) {
        return customsHeaderFeignExecute(() -> codeProjectFeign.getProject(groupId, path), token);
    }

    @Override
    public void addProjectMember(Integer projectId, List<ProjectMemberDto> members) {
        customsHeaderFeignExecute(() -> codeProjectFeign.addProjectMember(projectId, members), token);
    }

    @Override
    public void deleteProjectMember(Integer gitlabId, String username) {
        customsHeaderFeignExecute(() -> codeProjectFeign.delProjectMember(gitlabId, username), token);
    }

    @Override
    public void updateBatchProjectMember(Long userId, List<Integer> projectIds, Long roleId) {
        User userById = iamRepository.getUserById(userId);
        BatchProjectMemberDto  batchProjectMemberDto = new BatchProjectMemberDto();
        batchProjectMemberDto.setUsername(userById.getUsername());
        batchProjectMemberDto.setProjectIds(projectIds);
        batchProjectMemberDto.setRoleId(roleId);
        BatchProjectMemberResponse response = customsHeaderFeignExecute(() -> codeProjectFeign.updateBatchProjectMember(batchProjectMemberDto), token);
        if(CollectionUtils.isNotEmpty(response.getFail())){
            String s = JSONObject.toJSONString(response.getFail());
            log.error("仓库角色修改失败：" + s);
            throw new SystemException(ExceptionCode.REMOTE_SCM_FAIL, "仓库角色修改失败：" + s);
        }
    }

    @Override
    public ScanIssueWithHistoryDetailVO scanIssues(Integer gitlabId, String branchName) {
        return feignExecute(() -> codeProjectFeign.scanIssues(gitlabId, branchName));
    }

    @Override
    public void initCodeScan(Integer gitlabId, String technology) {
        String languageByCn = LanguageEnum.getLanguageByCn(technology);
        String scanType = LanguageEnum.getScanType(technology);
        if (StringUtils.isEmpty(languageByCn) || languageByCn.equals("其他")){
            return;
        }
        feignExecute(()-> codeProjectFeign.initByLanguage(gitlabId, scanType));
    }

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_SCM_FAIL;
    }

    @Override
    public List<String> envList(Long configId) {
        return customsHeaderFeignExecute(()-> codeProjectFeign.envList(configId), token);
    }

    @Override
    public GetConfigResponse getConfig(Long configId) {
        return customsHeaderFeignExecute(()-> codeProjectFeign.getConfig(configId), token);
    }
}
