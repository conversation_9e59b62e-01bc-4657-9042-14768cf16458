package cn.harmonycloud.development.outbound;

import cn.harmonycloud.development.pojo.entity.OnlineOrder;
import cn.harmonycloud.mybatis.base.BaseRepository;

public interface OnlineOrderRepository extends BaseRepository<OnlineOrder> {
    /**
     * 更新上机单编码
     * @param id
     * @param code
     */
    void updateCode(Long id, String code);

    void cancel(Long id);

    void deleteLogic(Long id, boolean b);
}
