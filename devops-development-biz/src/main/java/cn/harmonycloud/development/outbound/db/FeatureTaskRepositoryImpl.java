package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.FeatureTaskRepository;
import cn.harmonycloud.development.outbound.db.mapper.FeatureTaskMapper;
import cn.harmonycloud.development.pojo.entity.FeatureTask;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/21 9:19 上午
 **/
@Service
public class FeatureTaskRepositoryImpl extends BaseRepositoryImpl<FeatureTaskMapper, FeatureTask> implements FeatureTaskRepository {


    @Override
    public Map<Long, List<FeatureTask>> mapByFeatureId(List<Long> featureIds) {
        if(CollectionUtils.isEmpty(featureIds)){
            return new HashMap<>();
        }
        LambdaQueryWrapper<FeatureTask> query = new LambdaQueryWrapper<>();
        query.in(FeatureTask::getFeatureId, featureIds);
        List<FeatureTask> list = this.list(query);
        return list.stream().collect(Collectors.groupingBy(FeatureTask::getFeatureId));
    }

    @Override
    public List<FeatureTask> listByFeatureId(List<Long> featureId) {
        if(CollectionUtils.isEmpty(featureId)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FeatureTask> query = new LambdaQueryWrapper<>();
        query.in(FeatureTask::getFeatureId, featureId);
        return this.list(query);
    }

    @Override
    public List<Long> listIdByFeatureId(List<Long> featureId) {
        if(CollectionUtils.isEmpty(featureId)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FeatureTask> query = new LambdaQueryWrapper<>();
        query.in(FeatureTask::getFeatureId, featureId);
        return this.list(query).stream().map(ft -> Long.parseLong(ft.getTaskKey())).collect(Collectors.toList());
    }
}
