spring:
  main:
    allow-circular-references: true

  application:
    name: tasp-development-svc
    version: 1.0.0
  # MVC配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # Web资源配置
  web:
    resources:
      add-mappings: true
  # 云服务配置
  cloud:
    # 负载均衡配置
#    loadbalancer:
#      retry:
#        enabled: true
#      cache:
#        enabled: true
#      ribbon:
#        enabled: false
#      nacos:
#        enabled: true

    # Nacos服务发现配置
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:***********}:${NACOS_PORT:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:LUOcheng@384}
        namespace: ${NACOS_NS:dev}
      # Nacos配置中心配置
      config:
        contextPath: /nacos
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        file-extension: yml
        shared-configs:
          - ${spring.application.name}.${spring.cloud.nacos.config.file-extension}
        namespace: ${NACOS_NS:dev}

# Swagger配置
swagger:
  enabled: true

# OpenAPI 配置
project:
  openapi:
    name: "TASP Development Service"
    version: "v1.0"
    description: "开发服务API文档"
    group: "development"

    homepage:
      enable: true
      path: "/"
