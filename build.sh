#!/bin/bash

repository=10.120.1.233:8443
username=devops
password=Harbor12345
namespace=yanlian-hc
image=devops-development-svc

#docker buildx build -t $repository/$namespace/$image:$version-aarch64 -f Dockerfile_arm64 --platform linux/arm64 -o type=docker .
sudo docker buildx build -t $repository/$namespace/$image:tianhe-1.0.0.1 -f Dockerfile --platform linux/amd64 -o type=docker .

docker login $repository --username $username --password $password
#
docker push $repository/$namespace/$image:tianhe-1.0.0.1